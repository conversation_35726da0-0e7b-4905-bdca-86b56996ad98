import 'package:flutter/material.dart';
import 'package:test_pro/services/cart_service.dart';

// Simple cart state model
class CartState {
  final int itemCount;
  final double totalPrice;
  final bool isLoading;

  const CartState({
    this.itemCount = 0,
    this.totalPrice = 0.0,
    this.isLoading = false,
  });

  bool get hasItems => itemCount > 0;

  CartState copyWith({
    int? itemCount,
    double? totalPrice,
    bool? isLoading,
  }) {
    return CartState(
      itemCount: itemCount ?? this.itemCount,
      totalPrice: totalPrice ?? this.totalPrice,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartState &&
        other.itemCount == itemCount &&
        (other.totalPrice - totalPrice).abs() < 0.01 &&
        other.isLoading == isLoading;
  }

  @override
  int get hashCode => itemCount.hashCode ^ totalPrice.hashCode ^ isLoading.hashCode;
}

// Simple cart notifier using ValueNotifier
class SimpleCartNotifier extends ValueNotifier<CartState> {
  static final SimpleCartNotifier _instance = SimpleCartNotifier._internal();
  factory SimpleCartNotifier() => _instance;
  SimpleCartNotifier._internal() : super(const CartState());

  bool _isInitialized = false;

  // Initialize cart data
  Future<void> initialize() async {
    if (_isInitialized) return;
    await _updateCartData();
    _isInitialized = true;
  }

  // Update cart data from storage
  Future<void> _updateCartData() async {
    value = value.copyWith(isLoading: true);

    try {
      final count = await CartService.getCartItemCount();
      final total = await CartService.getTotalPrice();
      
      value = CartState(
        itemCount: count,
        totalPrice: total,
        isLoading: false,
      );
    } catch (e) {
      value = const CartState(
        itemCount: 0,
        totalPrice: 0.0,
        isLoading: false,
      );
    }
  }

  // Add item to cart and update
  Future<void> addToCart({
    required product,
    required int quantity,
    String? temperature,
    String? size,
    String? sweetness,
    String? topping,
  }) async {
    await CartService.addToCart(
      product: product,
      quantity: quantity,
      temperature: temperature,
      size: size,
      sweetness: sweetness,
      topping: topping,
    );
    await _updateCartData();
  }

  // Update quantity and refresh
  Future<void> updateQuantity(int productId, int newQuantity) async {
    await CartService.updateQuantity(productId, newQuantity);
    await _updateCartData();
  }

  // Remove item and refresh
  Future<void> removeFromCart(int productId) async {
    await CartService.removeFromCart(productId);
    await _updateCartData();
  }

  // Clear cart and refresh
  Future<void> clearCart() async {
    await CartService.clearCart();
    await _updateCartData();
  }

  // Refresh cart data
  Future<void> refresh() async {
    await _updateCartData();
  }
}

// Simple mixin using ValueListenableBuilder approach
mixin SimpleCartMixin<T extends StatefulWidget> on State<T> {
  final SimpleCartNotifier _cartNotifier = SimpleCartNotifier();
  
  @override
  void initState() {
    super.initState();
    // Initialize after first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _cartNotifier.initialize();
      }
    });
  }

  // Getters for easy access
  int get cartItemCount => _cartNotifier.value.itemCount;
  double get cartTotalPrice => _cartNotifier.value.totalPrice;
  bool get hasCartItems => _cartNotifier.value.hasItems;
  bool get isCartLoading => _cartNotifier.value.isLoading;
  SimpleCartNotifier get cartNotifier => _cartNotifier;

  // Methods for cart operations
  Future<void> addToCart({
    required product,
    required int quantity,
    String? temperature,
    String? size,
    String? sweetness,
    String? topping,
  }) async {
    await _cartNotifier.addToCart(
      product: product,
      quantity: quantity,
      temperature: temperature,
      size: size,
      sweetness: sweetness,
      topping: topping,
    );
  }

  Future<void> updateCartQuantity(int productId, int newQuantity) async {
    await _cartNotifier.updateQuantity(productId, newQuantity);
  }

  Future<void> removeFromCart(int productId) async {
    await _cartNotifier.removeFromCart(productId);
  }

  Future<void> clearCart() async {
    await _cartNotifier.clearCart();
  }

  Future<void> refreshCart() async {
    await _cartNotifier.refresh();
  }
}

// Widget that listens to cart changes
class CartListener extends StatelessWidget {
  final Widget Function(BuildContext context, CartState cartState, Widget? child) builder;
  final Widget? child;

  const CartListener({
    super.key,
    required this.builder,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<CartState>(
      valueListenable: SimpleCartNotifier(),
      builder: builder,
      child: child,
    );
  }
}

// Simple cart FAB that uses ValueListenableBuilder
class SimpleCartFAB extends StatelessWidget {
  final VoidCallback onPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final String? heroTag;

  const SimpleCartFAB({
    super.key,
    required this.onPressed,
    this.backgroundColor,
    this.foregroundColor,
    this.heroTag,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<CartState>(
      valueListenable: SimpleCartNotifier(),
      builder: (context, cartState, child) {
        if (!cartState.hasItems) {
          return const SizedBox.shrink();
        }

        return AnimatedScale(
          scale: cartState.hasItems ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.elasticOut,
          child: FloatingActionButton(
            heroTag: heroTag ?? "simple_cart_fab",
            onPressed: onPressed,
            backgroundColor: backgroundColor ?? Colors.red,
            foregroundColor: foregroundColor ?? Colors.white,
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                const Icon(Icons.shopping_cart, size: 28),
                Positioned(
                  right: -8,
                  top: -8,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      border: Border.fromBorderSide(
                        BorderSide(color: Colors.red, width: 1),
                      ),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 20,
                      minHeight: 20,
                    ),
                    child: Text(
                      cartState.itemCount > 99 ? '99+' : cartState.itemCount.toString(),
                      style: TextStyle(
                        color: backgroundColor ?? Colors.red,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
