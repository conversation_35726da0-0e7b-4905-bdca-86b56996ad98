import 'package:flutter/material.dart';

class SelectableCategory extends StatelessWidget {
  final String category;
  final bool isSelected;

  const SelectableCategory({
    super.key,
    required this.category,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: isSelected ? const Color.fromARGB(255, 0, 0, 0) : const Color.fromARGB(255, 209, 251, 255),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? const Color.fromARGB(255, 255, 200, 183) : Colors.transparent,
          width: 2,
        ),
      ),
      height: 30,
      width: 100,
      child: Center(
        child: Text(
          category,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.black,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
