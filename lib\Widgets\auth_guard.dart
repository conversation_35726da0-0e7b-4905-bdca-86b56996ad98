import 'package:flutter/material.dart';
import 'package:test_pro/services/auth_service.dart';

/// A widget that protects content behind authentication
/// Shows login alert for guest users when they try to access protected content
class AuthGuard extends StatelessWidget {
  final Widget child;
  final String? message;
  final VoidCallback? onTap;
  final bool showAlertOnTap;

  const AuthGuard({
    super.key,
    required this.child,
    this.message,
    this.onTap,
    this.showAlertOnTap = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!showAlertOnTap) {
      return child;
    }

    return GestureDetector(
      onTap: () async {
        final hasAuth = await AuthService.requireAuth(
          context,
          message: message ?? 'Please log in to access this feature.',
        );
        
        if (hasAuth && onTap != null) {
          onTap!();
        }
      },
      child: child,
    );
  }
}

/// A mixin that provides easy access to authentication checks
mixin AuthMixin {
  /// Check if user is authenticated and show alert if not
  Future<bool> requireAuth(BuildContext context, {String? message}) async {
    return await AuthService.requireAuth(context, message: message);
  }

  /// Show login alert with custom message
  void showLoginAlert(BuildContext context, {String? message}) {
    AuthService.showLoginAlert(context, message: message);
  }

  /// Check if user is logged in without showing alert
  Future<bool> isLoggedIn() async {
    return await AuthService().isLoggedIn();
  }

  /// Check if user is guest without showing alert
  Future<bool> isGuest() async {
    return await AuthService().isGuest();
  }
}

/// Extension on BuildContext for easy auth checks
extension AuthContext on BuildContext {
  /// Require authentication with alert
  Future<bool> requireAuth({String? message}) async {
    return await AuthService.requireAuth(this, message: message);
  }

  /// Show login alert
  void showLoginAlert({String? message}) {
    AuthService.showLoginAlert(this, message: message);
  }
}
