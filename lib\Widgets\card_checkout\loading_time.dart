import 'dart:async';
import 'package:flutter/material.dart';
import 'package:test_pro/screen/home_screen.dart';

class LoadingTime extends StatefulWidget {
  const LoadingTime({super.key});

  @override
  State<LoadingTime> createState() => _LoadingTimeState();
}

class _LoadingTimeState extends State<LoadingTime> {
  int _remainingSeconds = 60; // 1 minute
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _startCountdown();
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingSeconds > 0) {
        setState(() {
          _remainingSeconds--;
        });
      } else {
        _timer?.cancel();
        _goToNextPage();
      }
    });
  }

  void _goToNextPage() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => const HomeScreen(),
      ),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  String get _formattedTime {
    final minutes = (_remainingSeconds ~/ 60).toString().padLeft(2, '0');
    final seconds = (_remainingSeconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  Widget get _loadtime {
    return Row(
      children: [
        Icon(Icons.access_time, size: 16, color: Colors.blue[600]),
        const SizedBox(width: 4),
        Text(
          _formattedTime,
          style: const TextStyle(fontSize: 14, color: Colors.black),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return _loadtime;
  }
}

class NextPage extends StatelessWidget {
  const NextPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Next Page")),
      body: const Center(child: Text("Time’s up! Moved to Next Page.")),
    );
  }
}
