import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:test_pro/route/route_screen.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _rememberMe = false;
  bool _isValidMail = false;


  @override
  void dispose() {
    _usernameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: Colors.white,
        body: SingleChildScrollView(
          child: Column(
            children: [
              _topBar,
              Padding(
                padding: EdgeInsets.all(24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      _usernameField,
                      SizedBox(height: 10),
                      _emailField,
                      SizedBox(height: 10),
                      _passwordField,
                      SizedBox(height: 10),
                      _confirmPasswordField,
                      SizedBox(height: 6),
                      Row(
                        children: [
                          Checkbox(
                            value: _rememberMe,
                            onChanged: (value) {
                              setState(() {
                                _rememberMe = value ?? false;
                              });
                            },
                            activeColor: Color(0xFF6B7B47),
                          ),
                          Text(
                            'rememberMe'.tr,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 6),
                      SizedBox(
                        width: double.infinity,
                        height: 48,
                        child: ElevatedButton(
                          onPressed: () {
                            if (_formKey.currentState!.validate()) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'registerSuccess'.tr,
                                  ),
                                ),
                              );
                            }
                            Future.delayed(Duration(seconds: 1), () {
                              AppRoute.key.currentState?.pushNamed(
                                AppRoute.loginScreen,
                              );
                            });
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.redAccent,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: Text(
                            'register'.tr.toUpperCase(),
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 40),

                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'alreadyHaveAccount'.tr,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              AppRoute.key.currentState?.pushNamed(
                                AppRoute.loginScreen,
                              );
                            },
                            child: Text(
                              'login'.tr,
                              style: const TextStyle(
                                fontSize: 14,
                                color: Color(0xFF6B7B47),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget get _topBar => Container(
    width: double.infinity,
    height: 200,
    decoration: BoxDecoration(
      borderRadius: BorderRadius.only(
        bottomLeft: Radius.circular(10),
        bottomRight: Radius.circular(10),
      ),
      color: Color.fromARGB(255, 255, 86, 86),
    ),
    child: Stack(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 15),
          child: IconButton(
            onPressed: () {
              Navigator.pop(context);
            },
            icon: Icon(Icons.arrow_back, color: Colors.white),
          ),
        ),
        Positioned(
          right: -75,
          bottom: -85,
          child: Image.asset(
            "assets/images/coffee/cfe.png",
            width: 220,
            fit: BoxFit.contain,
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(16).copyWith(top: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 70),
              Text(
                '${'hi'.tr}, ${'welcome'.tr}!',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              SizedBox(height: 8),
              Expanded(
                child: Text(
                  'createAccount'.tr,
                  style: const TextStyle(fontSize: 14, color: Colors.white70),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ],
    ),
  );

  Widget get _usernameField => TextFormField(
    controller: _usernameController,
    validator: (value) {
      if (value == null || value.isEmpty) return "Please enter your username";
      return null;
    },
    decoration: InputDecoration(
      labelText: "Username",
      prefixIcon: Icon(Icons.person),
      border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
    ),
  );

  Widget get _emailField => TextFormField(
    onChanged: (value) {
      setState(() {
        _isValidMail = value.contains("@");
      });
    },
    controller: _emailController,
    keyboardType: TextInputType.emailAddress,
    decoration: InputDecoration(
      labelText: "Email",
      prefixIcon: Icon(Icons.email),
      suffixIcon: Icon(
        Icons.check_circle,
        color: _isValidMail ? Colors.green : Colors.grey,
      ),
      border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
    ),
    validator: (value) {
      if (value == null || value.isEmpty) return "Please enter your email";
      if (!RegExp(r'\S+@\S+\.\S+').hasMatch(value)) {
        return "Please enter a valid email";
      }
      return null;
    },
  );

  Widget get _passwordField => TextFormField(
    controller: _passwordController,
    obscureText: _obscurePassword,
    decoration: InputDecoration(
      labelText: "Password",
      prefixIcon: Icon(Icons.lock),
      suffixIcon: IconButton(
        icon: Icon(_obscurePassword ? Icons.visibility_off : Icons.visibility),
        onPressed: () {
          setState(() {
            _obscurePassword = !_obscurePassword;
          });
        },
      ),
      border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
    ),
    validator: (value) {
      if (value == null || value.isEmpty) return "Please enter your password";
      if (value.length < 6) return "Password must be at least 6 characters";
      return null;
    },
  );

  Widget get _confirmPasswordField => TextFormField(
    controller: _confirmPasswordController,
    obscureText: _obscureConfirmPassword,
    decoration: InputDecoration(
      labelText: "Confirm Password",
      prefixIcon: Icon(Icons.lock),
      suffixIcon: IconButton(
        icon: Icon(
          _obscureConfirmPassword ? Icons.visibility_off : Icons.visibility,
        ),
        onPressed: () {
          setState(() {
            _obscureConfirmPassword = !_obscureConfirmPassword;
          });
        },
      ),
      border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
    ),
    validator: (value) {
      if (value == null || value.isEmpty) return "Please confirm your password";
      if (value != _passwordController.text) return "Passwords do not match";
      return null;
    },
  );
}
