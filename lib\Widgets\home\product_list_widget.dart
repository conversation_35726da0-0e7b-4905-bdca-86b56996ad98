import 'package:flutter/material.dart';
import 'package:test_pro/models/home/<USER>';
import 'package:test_pro/screen/product_detail_screen.dart';
import 'package:test_pro/services/auth_service.dart';
import 'package:test_pro/services/favorite_service.dart';

class ProductListWidget extends StatefulWidget {
  final List<Product> products;

  const ProductListWidget({super.key, required this.products});

  @override
  State<ProductListWidget> createState() => _ProductListWidgetState();
}

class _ProductListWidgetState extends State<ProductListWidget> {
  Map<int, bool> _favoriteStates = {};

  @override
  void initState() {
    super.initState();
    _loadFavoriteStates();
  }

  Future<void> _loadFavoriteStates() async {
    final Map<int, bool> states = {};
    for (final product in widget.products) {
      final productId = product.id ?? 0;
      states[productId] = await FavoriteService.isFavorite(productId);
    }
    if (mounted) {
      setState(() {
        _favoriteStates = states;
      });
    }
  }

  Future<void> _toggleFavorite(Product product) async {
    // Check authentication first
    final hasAuth = await AuthService.requireAuth(
      context,
      message: 'Please log in to add items to your favorites.',
    );

    if (!hasAuth) return;

    await FavoriteService.toggleFavorite(product);
    final productId = product.id ?? 0;
    final isFavorite = await FavoriteService.isFavorite(productId);

    if (mounted) {
      setState(() {
        _favoriteStates[productId] = isFavorite;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isFavorite ? 'Added to favorites' : 'Removed from favorites',
          ),
          backgroundColor: isFavorite ? Colors.green : Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.products.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(20.0),
          child: Text(
            "No products found in this category",
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.all(16.0),
          child: Text(
            "Products",
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
        ),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 0.8,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
          ),
          itemCount: widget.products.length,
          itemBuilder: (context, index) {
            final product = widget.products[index];
            return _buildProductCard(context, product);
          },
        ),
      ],
    );
  }

  Widget _buildProductCard(BuildContext context, Product product) {
    return GestureDetector(
      onTap: () async {
        final navigator = Navigator.of(context);

        // Check authentication first
        final hasAuth = await AuthService.requireAuth(
          context,
          message: 'Please log in to view product details.',
        );

        if (!hasAuth) return;

        navigator.push(
          MaterialPageRoute(
            builder: (context) => ProductDetailScreen(product: product),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withAlpha(50),
              blurRadius: 6,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 3,
              child: Stack(
                children: [
                  Container(
                    width: double.infinity,
                    decoration: const BoxDecoration(
                      color: Color(0xFFF5F5F5),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                    ),
                  ),
                  Center(child: Image.asset(product.image ?? '')),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: GestureDetector(
                      onTap: () => _toggleFavorite(product),
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(200),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          _favoriteStates[product.id ?? 0] == true
                              ? Icons.favorite
                              : Icons.favorite_border,
                          color: _favoriteStates[product.id ?? 0] == true
                              ? Colors.red
                              : Colors.grey,
                          size: 20,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      product.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      product.category,
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),

                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "\$${product.price.toStringAsFixed(2)}",
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                            fontSize: 14,
                          ),
                        ),
                        Container(
                          width: 30,
                          height: 30,
                          decoration: BoxDecoration(
                            color: Colors.brown,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: IconButton(
                            padding: EdgeInsets.zero,
                            onPressed: () async {
                              final navigator = Navigator.of(context);

                              // Check authentication first
                              final hasAuth = await AuthService.requireAuth(
                                context,
                                message: 'Please log in to view product details.',
                              );

                              if (!hasAuth) return;

                              navigator.push(
                                MaterialPageRoute(
                                  builder: (context) =>
                                      ProductDetailScreen(product: product),
                                ),
                              );
                            },
                            icon: const Icon(
                              Icons.add,
                              size: 15,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
