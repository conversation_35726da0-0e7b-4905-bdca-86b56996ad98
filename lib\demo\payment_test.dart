import 'package:flutter/material.dart';
import 'package:test_pro/models/home/<USER>';
import 'package:test_pro/services/cart_notifier_service.dart';
import 'package:test_pro/Widgets/card_checkout/payment_button.dart';
import 'package:test_pro/screen/Card/cart_screen.dart';

class PaymentTest extends StatefulWidget {
  const PaymentTest({super.key});

  @override
  State<PaymentTest> createState() => _PaymentTestState();
}

class _PaymentTestState extends State<PaymentTest> with CartMixin {
  final List<String> _testResults = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _runTest();
    });
  }

  Future<void> _runTest() async {
    await _addTestResult('🧪 Starting Payment Cart Clear Test...\n');

    try {
      // Step 1: Clear cart first
      await _addTestResult('Step 1: Clearing cart...');
      await clearCart();
      await _addTestResult('✅ Cart cleared. Items: $cartItemCount\n');

      // Step 2: Add test items to cart
      await _addTestResult('Step 2: Adding test items to cart...');
      final testProduct = allProducts.first;
      
      await addToCart(
        product: testProduct,
        quantity: 2,
        temperature: 'Hot',
        size: 'Large',
        sweetness: 'Sweet',
        topping: 'Extra Foam',
      );
      
      await _addTestResult('✅ Added ${testProduct.name} x2');
      await _addTestResult('   Cart items: $cartItemCount');
      await _addTestResult('   Cart total: \$${cartTotalPrice.toStringAsFixed(2)}\n');

      // Step 3: Add another item
      if (allProducts.length > 1) {
        final secondProduct = allProducts[1];
        await addToCart(
          product: secondProduct,
          quantity: 1,
          temperature: 'Cold',
          size: 'Medium',
          sweetness: 'No Sugar',
          topping: 'None',
        );
        
        await _addTestResult('✅ Added ${secondProduct.name} x1');
        await _addTestResult('   Cart items: $cartItemCount');
        await _addTestResult('   Cart total: \$${cartTotalPrice.toStringAsFixed(2)}\n');
      }

      // Step 4: Simulate payment process
      await _addTestResult('Step 3: Simulating payment process...');
      await _addTestResult('   Items before payment: $cartItemCount');
      
      // Simulate the payment button logic
      final cartNotifier = CartNotifierService();
      await cartNotifier.clearCart();
      
      // Refresh our local state
      await refreshCart();
      
      await _addTestResult('✅ Payment processed and cart cleared');
      await _addTestResult('   Items after payment: $cartItemCount');
      await _addTestResult('   Cart total after payment: \$${cartTotalPrice.toStringAsFixed(2)}\n');

      // Step 5: Verify cart is empty
      if (cartItemCount == 0 && cartTotalPrice == 0.0) {
        await _addTestResult('🎉 TEST PASSED: Cart successfully cleared after payment!');
      } else {
        await _addTestResult('❌ TEST FAILED: Cart not properly cleared');
        await _addTestResult('   Expected: 0 items, \$0.00');
        await _addTestResult('   Actual: $cartItemCount items, \$${cartTotalPrice.toStringAsFixed(2)}');
      }

    } catch (e) {
      await _addTestResult('❌ TEST ERROR: $e');
    }
  }

  Future<void> _addTestResult(String result) async {
    setState(() {
      _testResults.add(result);
    });
    // Small delay to make the test more visible
    await Future.delayed(const Duration(milliseconds: 100));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Cart Clear Test'),
        backgroundColor: Colors.green[700],
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Test Results
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Test Results:',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.green[700],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: SingleChildScrollView(
                        child: Text(
                          _testResults.join('\n'),
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Current Cart Status
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 5,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Current Cart Status:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Items: $cartItemCount'),
                    Text('Total: \$${cartTotalPrice.toStringAsFixed(2)}'),
                  ],
                ),
                const SizedBox(height: 16),
                
                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const CartScreen(),
                            ),
                          ).then((_) => refreshCart());
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('View Cart'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => _runTest(),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green[700],
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Run Test Again'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
