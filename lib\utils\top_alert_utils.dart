import 'package:flutter/material.dart';

class TopAlertUtils {
  static OverlayEntry? _currentOverlay;

  static void _dismissCurrentAlert() {
    if (_currentOverlay != null && _currentOverlay!.mounted) {
      _currentOverlay!.remove();
      _currentOverlay = null;
    }
  }
  /// Show success message from the top of the screen
  static void showSuccessAlert(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 2),
    IconData icon = Icons.check_circle,
  }) {
    _showOverlayAlert(
      context,
      message,
      Colors.green,
      icon,
      duration,
    );
  }

  /// Show error message from the top of the screen
  static void showErrorAlert(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
    IconData icon = Icons.error,
  }) {
    _showOverlayAlert(
      context,
      message,
      Colors.red,
      icon,
      duration,
    );
  }

  /// Show warning message from the top of the screen
  static void showWarningAlert(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 2),
    IconData icon = Icons.warning,
  }) {
    _showOverlayAlert(
      context,
      message,
      Colors.orange,
      icon,
      duration,
    );
  }

  /// Show info message from the top of the screen
  static void showInfoAlert(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 2),
    IconData icon = Icons.info,
  }) {
    _showOverlayAlert(
      context,
      message,
      Colors.blue,
      icon,
      duration,
    );
  }

  /// Show custom alert from the top of the screen
  static void showCustomAlert(
    BuildContext context,
    String message, {
    required Color backgroundColor,
    Color textColor = Colors.white,
    IconData? icon,
    Duration duration = const Duration(seconds: 2),
  }) {
    _showOverlayAlert(
      context,
      message,
      backgroundColor,
      icon ?? Icons.info,
      duration,
    );
  }

  /// Show overlay alert that won't conflict with bottom elements
  static void _showOverlayAlert(
    BuildContext context,
    String message,
    Color backgroundColor,
    IconData icon,
    Duration duration,
  ) {
    // Dismiss any existing alert
    _dismissCurrentAlert();

    final overlay = Overlay.of(context);

    _currentOverlay = OverlayEntry(
      builder: (context) => _TopAlertWidget(
        message: message,
        backgroundColor: backgroundColor,
        icon: icon,
        onDismiss: () {
          _dismissCurrentAlert();
        },
      ),
    );

    overlay.insert(_currentOverlay!);

    // Auto dismiss after duration
    Future.delayed(duration, () {
      _dismissCurrentAlert();
    });
  }
}

/// Custom widget for top alerts using overlay
class _TopAlertWidget extends StatefulWidget {
  final String message;
  final Color backgroundColor;
  final IconData icon;
  final VoidCallback onDismiss;

  const _TopAlertWidget({
    required this.message,
    required this.backgroundColor,
    required this.icon,
    required this.onDismiss,
  });

  @override
  State<_TopAlertWidget> createState() => _TopAlertWidgetState();
}

class _TopAlertWidgetState extends State<_TopAlertWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_controller);

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 10,
      left: 16,
      right: 16,
      child: SlideTransition(
        position: _slideAnimation,
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: widget.backgroundColor,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Icon(widget.icon, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      widget.message,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
