import 'package:flutter/material.dart';
import 'package:test_pro/services/theme_service.dart';

class ThemeToggleButton extends StatelessWidget {
  final bool showLabel;
  final double iconSize;

  const ThemeToggleButton({
    super.key,
    this.showLabel = false,
    this.iconSize = 24,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: ThemeService(),
      builder: (context, child) {
        return GestureDetector(
          onTap: () async {
            await ThemeService().toggleTheme();
          },
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
            ),
            child: showLabel
                ? Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        ThemeService().themeIcon,
                        size: iconSize,
                        color: Theme.of(context).iconTheme.color,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        ThemeService().themeModeString,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  )
                : Icon(
                    ThemeService().themeIcon,
                    size: iconSize,
                    color: Theme.of(context).iconTheme.color,
                  ),
          ),
        );
      },
    );
  }
}

class FloatingThemeToggle extends StatelessWidget {
  const FloatingThemeToggle({super.key});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 10,
      right: 16,
      child: AnimatedBuilder(
        animation: ThemeService(),
        builder: (context, child) {
          return FloatingActionButton.small(
            onPressed: () async {
              await ThemeService().toggleTheme();
            },
            backgroundColor: Theme.of(context).primaryColor,
            foregroundColor: Theme.of(context).colorScheme.onPrimary,
            child: Icon(ThemeService().themeIcon),
          );
        },
      ),
    );
  }
}
