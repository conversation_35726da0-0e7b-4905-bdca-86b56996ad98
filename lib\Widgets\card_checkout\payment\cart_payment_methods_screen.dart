import 'package:flutter/material.dart';
import 'package:test_pro/models/cart_model.dart';
import 'package:test_pro/Widgets/card_checkout/payment/cart_credit_card_payment_screen.dart';

class CartPaymentMethodsScreen extends StatefulWidget {
  final List<CartItem> cartItems;

  const CartPaymentMethodsScreen({
    super.key,
    required this.cartItems,
  });

  @override
  State<CartPaymentMethodsScreen> createState() => _CartPaymentMethodsScreenState();
}

class _CartPaymentMethodsScreenState extends State<CartPaymentMethodsScreen> {
  String? selectedMethod;

  void _navigateToPaymentDetails(PaymentMethodItem method) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CartCreditCardPaymentScreen(
          cartItems: widget.cartItems,
          paymentMethod: method.title,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.grey[50],
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Payment Method',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Choose your payment method',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: ListView.builder(
                itemCount: _paymentMethods.length,
                itemBuilder: (context, index) {
                  final method = _paymentMethods[index];
                  final isSelected = selectedMethod == method.id;
                  
                  return Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          selectedMethod = method.id;
                        });
                        
                        // Navigate to payment details screen after a short delay
                        Future.delayed(const Duration(milliseconds: 200), () {
                          if (mounted) {
                            _navigateToPaymentDetails(method);
                          }
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isSelected ? Colors.green : Colors.grey[300]!,
                            width: isSelected ? 2 : 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            Container(
                              width: 48,
                              height: 48,
                              decoration: BoxDecoration(
                                color: method.color.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                method.icon,
                                color: method.color,
                                size: 24,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    method.title,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    method.subtitle,
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            if (isSelected)
                              Container(
                                width: 24,
                                height: 24,
                                decoration: const BoxDecoration(
                                  color: Colors.green,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.check,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class PaymentMethodItem {
  final String id;
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;

  PaymentMethodItem({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
  });
}

final List<PaymentMethodItem> _paymentMethods = [
  PaymentMethodItem(
    id: 'credit_card',
    title: 'Credit/Debit Card',
    subtitle: 'Visa, Mastercard, American Express',
    icon: Icons.credit_card,
    color: Colors.blue,
  ),
  PaymentMethodItem(
    id: 'paypal',
    title: 'PayPal',
    subtitle: 'Pay with your PayPal account',
    icon: Icons.account_balance_wallet,
    color: Colors.indigo,
  ),
  PaymentMethodItem(
    id: 'apple_pay',
    title: 'Apple Pay',
    subtitle: 'Touch ID or Face ID required',
    icon: Icons.phone_iphone,
    color: Colors.black,
  ),
  PaymentMethodItem(
    id: 'google_pay',
    title: 'Google Pay',
    subtitle: 'Pay with Google',
    icon: Icons.account_balance_wallet_outlined,
    color: Colors.green,
  ),
];
