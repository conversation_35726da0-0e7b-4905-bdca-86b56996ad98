# 🌐 Khmer Translation Guide for Coffee Vortex App

## ✅ **Translation System Setup Complete!**

Your app now has a comprehensive Khmer translation system using GetX internationalization. Here's everything you need to know:

## 📁 **Files Created/Updated**

### 1. **Translation Files**
- `lib/screen/translate/app_translate.dart` - **EXPANDED** with 200+ translations
- `lib/services/translation_service.dart` - **NEW** helper service
- `lib/main.dart` - **UPDATED** to initialize translations
- `lib/Widgets/translate_screen.dart` - **UPDATED** with new language selector

### 2. **Example Implementation**
- `lib/screen/Login/login_screen.dart` - **UPDATED** as example

## 🔧 **How to Use Translations**

### **Basic Usage**
```dart
import 'package:get/get.dart';

// Simple translation
Text('login'.tr)  // Shows "Login" or "ចូលប្រើប្រាស់"

// Multiple translations in one string
Text('${'hi'.tr}, ${'welcome'.tr}!')  // "Hi, Welcome!" or "សួស្តី, សូមស្វាគមន៍!"
```

### **Advanced Usage with Translation Service**
```dart
import 'package:test_pro/services/translation_service.dart';

// Using the helper service
Text(TranslationService.tr('login'))

// With arguments
Text(TranslationService.tr('searchResultsFor', args: {'query': 'coffee'}))

// Currency formatting
Text(TranslationService.formatCurrency(4.50))  // "$4.50" or "4.50 រៀល"

// Date formatting
Text(TranslationService.formatDate(DateTime.now()))
```

## 📝 **Available Translation Keys**

### **Authentication**
- `login`, `register`, `logout`
- `email`, `password`, `confirmPassword`
- `forgotPassword`, `rememberMe`
- `signUp`, `signIn`, `createAccount`
- `loginSuccess`, `loginFailed`
- `pleaseLogin`

### **Navigation**
- `home`, `cart`, `allProducts`, `favorites`, `profile`
- `notifications`, `orders`, `outlets`, `settings`
- `about`, `help`, `contactUs`

### **Products & Shopping**
- `products`, `categories`, `price`, `quantity`
- `addToCart`, `buyNow`, `checkout`
- `outOfStock`, `inStock`
- `productDetails`, `reviews`, `rating`

### **Coffee Types**
- `coffee`, `tea`, `coldCoffee`, `hotCoffee`
- `espresso`, `latte`, `cappuccino`, `americano`
- `mocha`, `macchiato`, `frappuccino`
- `coldBrew`, `icedLatte`, `greenTea`

### **Cart & Payment**
- `shoppingCart`, `cartEmpty`, `cartTotal`
- `subtotal`, `tax`, `shipping`, `total`
- `payment`, `paymentMethod`, `creditCard`
- `paypal`, `applePay`, `googlePay`
- `paymentSuccessful`, `paymentFailed`

### **Search**
- `search`, `searchProducts`, `searchForCoffee`
- `noResultsFound`, `searchResults`
- `recentSearches`, `clearSearch`

### **Profile & Settings**
- `myProfile`, `editProfile`, `personalInfo`
- `firstName`, `lastName`, `phoneNumber`
- `changePassword`, `accountSettings`

### **General**
- `welcome`, `back`, `next`, `cancel`, `confirm`
- `save`, `delete`, `edit`, `loading`
- `error`, `success`, `warning`, `ok`

## 🎯 **How to Add New Translations**

### 1. **Add to Translation File**
Edit `lib/screen/translate/app_translate.dart`:

```dart
'en_US': {
  // Add your new key
  'newKey': 'English Text',
  // ... existing translations
},
'kh_KM': {
  // Add Khmer translation
  'newKey': 'ខ្មែរ Text',
  // ... existing translations
},
```

### 2. **Use in Your Widget**
```dart
Text('newKey'.tr)
```

## 🔄 **Language Switching**

### **Programmatic Language Change**
```dart
// Change to Khmer
await TranslationService.changeLanguage(Locale('kh', 'KM'));

// Change to English
await TranslationService.changeLanguage(Locale('en', 'US'));

// Toggle between languages
await TranslationService.toggleLanguage();
```

### **Show Language Selection Dialog**
```dart
TranslationService.showLanguageDialog(context);
```

### **Check Current Language**
```dart
if (TranslationService.isKhmer) {
  // Do something for Khmer users
}

if (TranslationService.isEnglish) {
  // Do something for English users
}
```

## 🎨 **UI Examples**

### **Button with Translation**
```dart
ElevatedButton(
  onPressed: () {},
  child: Text('login'.tr),
)
```

### **Form Field with Translation**
```dart
TextFormField(
  decoration: InputDecoration(
    labelText: 'email'.tr,
    hintText: 'enterEmail'.tr,
  ),
)
```

### **AppBar with Translation**
```dart
AppBar(
  title: Text('allProducts'.tr),
)
```

### **Alert Dialog with Translation**
```dart
AlertDialog(
  title: Text('confirm'.tr),
  content: Text('pleaseLogin'.tr),
  actions: [
    TextButton(
      onPressed: () => Navigator.pop(context),
      child: Text('cancel'.tr),
    ),
    TextButton(
      onPressed: () {},
      child: Text('ok'.tr),
    ),
  ],
)
```

## 🚀 **Quick Implementation Steps**

### **For Any Screen:**

1. **Add GetX import:**
```dart
import 'package:get/get.dart';
```

2. **Replace hardcoded text:**
```dart
// Before
Text('Login')

// After
Text('login'.tr)
```

3. **For complex strings:**
```dart
// Before
Text('Search results for "coffee" (5 found)')

// After
Text('${'searchResultsFor'.tr} "$query" (${results.length} ${'found'.tr})')
```

## 📱 **Language Persistence**

The selected language is automatically saved and restored when the app restarts. Users don't need to reselect their language preference.

## 🎯 **Next Steps**

1. **Update your screens** - Replace hardcoded text with translation keys
2. **Test both languages** - Switch between English and Khmer to verify
3. **Add more translations** - Expand the translation file as needed
4. **Customize fonts** - Add Khmer fonts for better text rendering

## 🔍 **Testing**

1. **Run the app**
2. **Go to Settings** (use the SettingView widget)
3. **Tap Language** 
4. **Select ខ្មែរ (Khmer)**
5. **See all translated text change instantly!**

Your app now supports full Khmer localization! 🇰🇭✨
