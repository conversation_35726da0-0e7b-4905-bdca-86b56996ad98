import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:test_pro/models/user_model.dart';
import 'package:test_pro/services/auth_service.dart';

class UserProfileService {
  static const String _profileKey = 'user_profile';
  
  // Singleton pattern
  static final UserProfileService _instance = UserProfileService._internal();
  factory UserProfileService() => _instance;
  UserProfileService._internal();

  /// Get user profile from storage
  Future<UserProfile?> getUserProfile() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileJson = prefs.getString(_profileKey);

      if (profileJson != null) {
        final profileMap = jsonDecode(profileJson) as Map<String, dynamic>;
        return UserProfile.fromMap(profileMap);
      }

      // Check if user is logged in
      final isLoggedIn = await AuthService().isLoggedIn();

      if (isLoggedIn) {
        // Return profile with stored credentials for logged-in users
        return UserProfile(
          name: prefs.getString('username') ?? 'User',
          email: prefs.getString('email') ?? '',
          phone: '',
        );
      } else {
        // Always return guest profile for non-logged-in users
        // This allows the app to work without requiring login
        return UserProfile(
          name: 'Guest',
          email: '',
          phone: '',
        );
      }
    } catch (e) {
      // Always return guest profile on error - never block the app
      return UserProfile(
        name: 'Guest',
        email: '',
        phone: '',
      );
    }
  }

  /// Save user profile to storage
  Future<bool> saveUserProfile(UserProfile profile) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileJson = jsonEncode(profile.toMap());

      // Save profile (allow saving for both logged-in users and guests)
      await prefs.setString(_profileKey, profileJson);

      // Check if user is logged in to decide what else to save
      final isLoggedIn = await AuthService().isLoggedIn();

      if (isLoggedIn) {
        // For logged-in users, also update individual fields for backward compatibility
        await prefs.setString('username', profile.name);
        await prefs.setString('email', profile.email);
      } else {
        // For guest users, only save the profile data (not auth fields)
        // This allows guests to customize their profile without affecting auth state
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Update specific profile field
  Future<bool> updateProfileField(String field, String value) async {
    try {
      final currentProfile = await getUserProfile();
      if (currentProfile == null) return false;

      UserProfile updatedProfile;
      switch (field) {
        case 'name':
          updatedProfile = currentProfile.copyWith(name: value);
          break;
        case 'email':
          updatedProfile = currentProfile.copyWith(email: value);
          break;
        case 'phone':
          updatedProfile = currentProfile.copyWith(phone: value);
          break;
        case 'profileImage':
          updatedProfile = currentProfile.copyWith(profileImage: value);
          break;
        default:
          return false;
      }

      return await saveUserProfile(updatedProfile);
    } catch (e) {
      return false;
    }
  }

  /// Clear user profile
  Future<void> clearUserProfile() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_profileKey);
  }

  /// Check if profile is complete
  Future<bool> isProfileComplete() async {
    final profile = await getUserProfile();
    if (profile == null) return false;
    
    return profile.name.isNotEmpty && 
           profile.email.isNotEmpty && 
           profile.phone.isNotEmpty;
  }
}
