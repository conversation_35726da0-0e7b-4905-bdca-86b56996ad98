import 'package:flutter/material.dart';
import 'package:test_pro/Widgets/orders/alert_order.dart';
import 'package:test_pro/services/cart_notifier_service.dart';

class PaymentButton extends StatefulWidget {
  const PaymentButton({super.key});

  @override
  State<PaymentButton> createState() => _PaymentButtonState();
}

class _PaymentButtonState extends State<PaymentButton> {
  bool _isProcessing = false;

  Future<void> _processPayment() async {
    if (_isProcessing) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      // Simulate payment processing
      await Future.delayed(const Duration(seconds: 1));

      // Clear the cart after successful payment
      final cartNotifier = CartNotifierService();
      await cartNotifier.clearCart();

      // Generate a random order number for demo
      final orderNumber = 'CF${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}';

      if (mounted) {
        // Navigate to success screen
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => AlertOrder(
              isSuccess: true,
              orderNumber: orderNumber,
              onBackToHome: () {
                // Navigate back to home and clear navigation stack
                Navigator.of(context).popUntil((route) => route.isFirst);
              },
              onCheckOrderStatus: () {
                // For now, just go back to home
                Navigator.of(context).popUntil((route) => route.isFirst);
              },
            ),
          ),
        );
      }
    } catch (e) {
      // Handle payment failure
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AlertOrder(
              isSuccess: false,
              errorMessage: 'Payment failed. Please try again.',
              onRetry: () {
                Navigator.pop(context);
              },
              onBackToHome: () {
                Navigator.of(context).popUntil((route) => route.isFirst);
              },
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SizedBox(
        width: double.infinity,
        height: 56,
        child: ElevatedButton(
          onPressed: _isProcessing ? null : _processPayment,
          style: ElevatedButton.styleFrom(
            backgroundColor: _isProcessing ? Colors.grey : Colors.green[700],
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: _isProcessing
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text(
                  'PAYMENT',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
        ),
      ),
    );
  }
}
