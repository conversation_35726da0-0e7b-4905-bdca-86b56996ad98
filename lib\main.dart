import 'package:flutter/material.dart';
import 'package:test_pro/data/db_manager.dart';
import 'package:test_pro/route/route_screen.dart';
import 'package:test_pro/screen/splash_screen.dart';
import 'package:get/get.dart';
import 'package:test_pro/screen/translate/app_translate.dart';
import 'package:test_pro/services/theme_service.dart';
import 'package:test_pro/services/translation_service.dart';
import 'package:test_pro/controllers/language_controller.dart';
import 'package:test_pro/theme/app_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  DBManager.instance.database;

  // Initialize theme service
  await ThemeService().initTheme();

  // Initialize translation service
  await TranslationService.init();

  // Initialize language controller
  Get.put(LanguageController());

  // await insertProduct();

  runApp(const MyApp());
}

Future<void> insertProduct() async {
  await DBManager.instance.database;

  // Insert sample data
//   await db.insert("tbl_product", {
//     'name': 'Laptop',
//     'price': 899.99,
//     'quantity': 10,
//   });

//   await db.insert("tbl_product", {
//     'name': 'Phone',
//     'price': 499.50,
//     'quantity': 25,
//   });

//   // Query and print all products
//   final products = await db.query("tbl_product");
//   for (var product in products) {
//     print("Product: $product");
//   }
 }


class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: ThemeService(),
      builder: (context, child) {
        return Obx(() => GetMaterialApp(
          translations: AppTranslation(),
          locale: Get.find<LanguageController>().currentLocale,
          fallbackLocale: const Locale('en', 'US'),
          debugShowCheckedModeBanner: false,
          title: 'Coffee Vortex',
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeService().themeMode,
          home: const SplashScreen(),
          initialRoute: AppRoute.splashScreen,
          onGenerateRoute: AppRoute.onGenerateRoute,
          navigatorKey: AppRoute.key,
        ));
      },
    );
  }
}
