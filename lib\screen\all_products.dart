import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:test_pro/Widgets/home/<USER>';
import 'package:test_pro/Widgets/home/<USER>';
import 'package:test_pro/models/home/<USER>';
import 'package:test_pro/services/search_service.dart';

class AllProducts extends StatefulWidget {
  const AllProducts({super.key});

  @override
  State<AllProducts> createState() => _AllProductsState();
}

class _AllProductsState extends State<AllProducts> {
  String? selectedCategory;
  final TextEditingController _searchController = TextEditingController();
  List<Product> _allProducts = [];
  List<Product> _filteredProducts = [];
  Timer? _debounceTimer;
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _loadProducts();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _loadProducts() {
    // Load products from SearchService to get the complete list
    _allProducts = SearchService.getAllProducts();
    _applyFilters();
  }

  void _applyFilters() {
    List<Product> products = _allProducts;

    // Apply search filter
    if (_searchController.text.isNotEmpty) {
      products = SearchService.searchProducts(_searchController.text);
    }

    // Apply category filter
    if (selectedCategory != null) {
      products = products
          .where((product) => product.category == selectedCategory)
          .toList();
    }

    setState(() {
      _filteredProducts = products;
    });
  }

  void _performSearch(String query) {
    // Cancel previous timer
    _debounceTimer?.cancel();

    // Set up new timer for debouncing
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      setState(() {
        _isSearching = query.isNotEmpty;
      });
      _applyFilters();

      // Add to recent searches if query is not empty
      if (query.trim().isNotEmpty) {
        SearchService.addToRecentSearches(query);
      }
    });
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _isSearching = false;
    });
    _applyFilters();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text('allProducts'.tr),
        backgroundColor: const Color.fromARGB(255, 255, 255, 255),
        elevation: 0,
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'searchForCoffee'.tr,
                hintStyle: TextStyle(color: Colors.grey[600]),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
                contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                prefixIcon: Icon(Icons.search, color: Colors.grey[600], size: 22),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: Icon(Icons.clear, color: Colors.grey[600], size: 20),
                        onPressed: _clearSearch,
                      )
                    : null,
              ),
              onChanged: _performSearch,
            ),
          ),

          // Categories
          CategoriesWidget(
            onCategorySelected: (category) {
              setState(() {
                selectedCategory = category;
              });
              _applyFilters();
            },
          ),

          // Search Results Info
          if (_isSearching && _searchController.text.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              color: Colors.grey[50],
              child: Row(
                children: [
                  Icon(Icons.search, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 8),
                  Text(
                    '${'searchResultsFor'.tr} "${_searchController.text}" (${_filteredProducts.length} ${'found'.tr})',
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),

          // Products List
          Expanded(
            child: _filteredProducts.isEmpty && _searchController.text.isNotEmpty
                ? _buildNoResultsWidget()
                : ProductListWidget(products: _filteredProducts),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'noResultsFound'.tr,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${'tryDifferentKeywords'.tr}\n${'browseCategories'.tr}',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _clearSearch,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green[700],
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: Text(
              'clearSearch'.tr,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}