import 'package:flutter/material.dart';
import 'package:test_pro/Widgets/home/<USER>';
import 'package:test_pro/Widgets/home/<USER>';
import 'package:test_pro/models/home/<USER>';

class AllProducts extends StatefulWidget {
  const AllProducts({super.key});

  @override
  State<AllProducts> createState() => _AllProductsState();
}

class _AllProductsState extends State<AllProducts> {
  String? selectedCategory;
  @override
  Widget build(BuildContext context) {
    List<Product> filteredProducts = selectedCategory == null
        ? allProducts
        : allProducts
              .where((product) => product.category == selectedCategory)
              .toList();
    return Scaffold(
      appBar: AppBar(
        title: const Text('All Products'),
        backgroundColor: const Color.fromARGB(255, 255, 255, 255),
      ),
      body: Column(
        children: [
          CategoriesWidget(
            onCategorySelected: (category) {
              setState(() {
                selectedCategory = category;
              });
            },
          ),
          ProductListWidget(products: filteredProducts),
        ],
      ),
    );
  }
}