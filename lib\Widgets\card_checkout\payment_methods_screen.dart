import 'package:flutter/material.dart';
import 'package:test_pro/models/home/<USER>';
import 'package:test_pro/Widgets/card_checkout/credit_card_payment_screen.dart';

class PaymentMethodsScreen extends StatefulWidget {
  final Product product;
  final int quantity;
  final String? selectedTemperature;
  final String? selectedSize;
  final String? selectedSweetness;
  final String? selectedTopping;

  const PaymentMethodsScreen({
    super.key,
    required this.product,
    this.quantity = 1,
    this.selectedTemperature,
    this.selectedSize,
    this.selectedSweetness,
    this.selectedTopping,
  });

  @override
  State<PaymentMethodsScreen> createState() => _PaymentMethodsScreenState();
}

class _PaymentMethodsScreenState extends State<PaymentMethodsScreen> {
  String? selectedMethod;

  void _navigateToPaymentDetails(PaymentMethodItem method) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreditCardPaymentScreen(
          product: widget.product,
          quantity: widget.quantity,
          selectedTemperature: widget.selectedTemperature,
          selectedSize: widget.selectedSize,
          selectedSweetness: widget.selectedSweetness,
          selectedTopping: widget.selectedTopping,
          paymentMethod: method.title,
        ),
      ),
    );
  }

  final List<PaymentMethodItem> paymentMethods = [
    PaymentMethodItem(
      id: 'aba_khqr',
      title: 'ABA KHQR',
      subtitle: 'Scan to pay with any banking app',
      icon: Icons.account_balance,
      color: const Color(0xFF1E3A8A), // Blue
      iconColor: Colors.white,
    ),
    PaymentMethodItem(
      id: 'credit',
      title: 'Credit Card',
      subtitle: 'Visa',
      icon: Icons.credit_card,
      color: const Color(0xFF059669), // Green
      iconColor: Colors.white,
      showCardLogos: true,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.grey[50],
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.green[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.shopping_cart,
                color: Colors.green[600],
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            const Text(
              'Shopping',
              style: TextStyle(
                color: Colors.black,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.all(20),
            child: Text(
              'Choose way to pay',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              itemCount: paymentMethods.length,
              itemBuilder: (context, index) {
                final method = paymentMethods[index];
                final isSelected = selectedMethod == method.id;

                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: Material(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    elevation: isSelected ? 2 : 0,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(12),
                      onTap: () {
                        setState(() {
                          selectedMethod = method.id;
                        });

                        // Navigate to payment details screen after a short delay
                        Future.delayed(const Duration(milliseconds: 200), () {
                          if (mounted) {
                            _navigateToPaymentDetails(method);
                          }
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isSelected
                                ? Colors.green[400]!
                                : Colors.grey[200]!,
                            width: isSelected ? 2 : 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            // Payment Method Icon
                            Container(
                              width: 48,
                              height: 48,
                              decoration: BoxDecoration(
                                color: method.color,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                method.icon,
                                color: method.iconColor,
                                size: 24,
                              ),
                            ),

                            const SizedBox(width: 16),

                            // Payment Method Details
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    method.title,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.black87,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    method.subtitle,
                                    style: TextStyle(
                                      fontSize: 13,
                                      color: Colors.grey[600],
                                    ),
                                  ),

                                  // Card logos for credit/debit option
                                  if (method.showCardLogos) ...[
                                    const SizedBox(height: 8),
                                    Row(
                                      children: [
                                        _buildCardLogo(
                                          'VISA',
                                          Colors.blue[700]!,
                                        ),
                                        // const SizedBox(width: 8),
                                        // _buildCardLogo('MC', Colors.red[600]!),
                                        // const SizedBox(width: 8),
                                        // _buildCardLogo('JCB', Colors.green[600]!),
                                      ],
                                    ),
                                  ],
                                ],
                              ),
                            ),

                            // Arrow Icon
                            Icon(
                              Icons.chevron_right,
                              color: Colors.grey[400],
                              size: 20,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardLogo(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
          color: color,
        ),
      ),
    );
  }
}

class PaymentMethodItem {
  final String id;
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final Color iconColor;
  final bool showCardLogos;

  PaymentMethodItem({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.iconColor,
    this.showCardLogos = false,
  });
}
