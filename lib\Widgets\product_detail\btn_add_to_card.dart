import 'package:flutter/material.dart';
import 'package:test_pro/models/home/<USER>';
import 'package:test_pro/services/auth_service.dart';
import 'package:test_pro/Widgets/card_checkout/payment_methods_screen.dart';

class AddToCartButton extends StatelessWidget {
  final Product product;
  final int quantity;
  final String? selectedTemperature;
  final String? selectedSize;
  final String? selectedSweetness;
  final String? selectedTopping;
  final VoidCallback? onPressed;

  const AddToCartButton({
    super.key,
    required this.product,
    this.quantity = 1,
    this.selectedTemperature,
    this.selectedSize,
    this.selectedSweetness,
    this.selectedTopping,
    this.onPressed,
  });

  Future<void> _addToCart(BuildContext context) async {
    // Check authentication first
    final hasAuth = await AuthService.requireAuth(
      context,
      message: 'Please log in to continue with your purchase.',
    );

    if (!hasAuth) return;

    // Navigate to payment method selection instead of adding to cart
    if (context.mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PaymentMethodsScreen(
            product: product,
            quantity: quantity,
            selectedTemperature: selectedTemperature,
            selectedSize: selectedSize,
            selectedSweetness: selectedSweetness,
            selectedTopping: selectedTopping,
          ),
        ),
      );
    }
    onPressed?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SizedBox(
        width: double.infinity,
        height: 56,
        child: ElevatedButton(
          onPressed: () => _addToCart(context),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green[700],
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Icon(Icons.shopping_cart, color: Colors.white),
              const Text(
                "Buy Now",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Text(
                "\$${(product.price * quantity).toStringAsFixed(2)}",
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}