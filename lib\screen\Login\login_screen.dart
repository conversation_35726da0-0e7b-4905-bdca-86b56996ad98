import 'package:flutter/material.dart';
import 'package:test_pro/data/app_share_pref.dart';
import 'package:test_pro/route/route_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isValidMail = false;
  bool _isValidPassword = false;
  bool _isChecked = false;
   bool _rememberMe = false;
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  var imageIcon = {
    "facebook": "assets/images/facebook.png",
    "google": "assets/images/google.png",
    "iphone": "assets/images/iphone.png",
  };

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Column(
          children: [
            _topBar,
            Center(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(height: 32),
                      _emailField,
                      const SizedBox(height: 16),
                      _passwordField,
                      const SizedBox(height: 2),
                      _buildCheck,
                      _loginButton,
                      const SizedBox(height: 15),
                      _textLoginWith,
                      //_socialLogin,
                      _navigateToRegister,
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget get _buildCheck {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          
          Checkbox(
            value: _isValidMail && _isValidPassword && _rememberMe,
            isError: false,
            onChanged: (value) {
              setState(() {
                _isChecked = value!;
                if (_isChecked) {
                   _rememberMe = value;
                  _emailController.text = "<EMAIL>";
                  _passwordController.text = "123456";
                } else {
                  _emailController.clear();
                  _passwordController.clear();
                }
              });
            },
          ),
          TextButton(
            onPressed: () {
              AppRoute.key.currentState?.pushNamed(AppRoute.forgotPassword);
            },
            child: Text('Forgot Password?'),
          ),
        ],
      ),
    );
  }
  
  Widget get _topBar {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(10),
          bottomRight: Radius.circular(10),
        ),
        color: const Color.fromARGB(255, 255, 86, 86),
      ),
      child: Stack(
        children: [
          Positioned(
            right: -75,
            bottom: -85,
            child: Image.asset(
              "assets/images/coffee/cfe.png",
              width: 220,
              fit: BoxFit.contain,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16).copyWith(top: 15),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //IconButton(onPressed: (){}, icon: Icon(Icons.arrow_back)),
                SizedBox(height: 70),
                Text(
                  "Hi, Welcome back!",
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 8),
                Expanded(
                  child: Text(
                    "Lorem ipsum dolor sit amet, consectetur\nadipiscing elit, sed do eiusmod.",
                    style: TextStyle(fontSize: 14, color: Colors.white70),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget get _textLoginWith {
    return GestureDetector(
      onTap: () {
        AppRoute.key.currentState?.pushNamed(AppRoute.mainScreen);
      },
      child: Text('Continue As Guess!'),
    );
  }

  Widget get _emailField {
    return TextFormField(
      onChanged: (value) {
        if (value.contains("@gmail")) {
          setState(() {
            _isValidMail = true;
          });
        } else {
          setState(() {
            _isValidMail = false;
          });
        }
      },
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      decoration: InputDecoration(
        labelText: "Email",
        prefixIcon: const Icon(Icons.email),
        suffix: _isValidMail
            ? Icon(Icons.check_circle, color: Colors.green)
            : Icon(Icons.check_circle, color: Colors.grey),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return "Please enter your email";
        }
        if (!RegExp(r'\S+@\S+\.\S+').hasMatch(value)) {
          return "Please enter a valid email";
        }
        return null;
      },
    );
  }

  Widget get _passwordField {
    return TextFormField(
      onChanged: (value) {
        if (value.length >= 6) {
          setState(() {
            _isValidPassword = true;
          });
        } else {
          setState(() {
            _isValidPassword = false;
          });
        }
      },
      controller: _passwordController,
      obscureText: true,
      decoration: InputDecoration(
        labelText: "Password",
        prefixIcon: const Icon(Icons.lock),
        suffix: _isValidPassword
            ? Icon(Icons.check_circle, color: Colors.green)
            : Icon(Icons.check_circle, color: Colors.grey),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return "Please enter your password";
        }
        if (value.length < 6) {
          return "Password must be at least 6 characters";
        }
        return null;
      },
    );
  }

  Widget get _navigateToRegister {
    return Padding(
      padding: EdgeInsets.only(top: 30),
      child: TextButton(
        onPressed: () {
          AppRoute.key.currentState?.pushNamed(AppRoute.registerScreen);
        },
        child: Text("Don't have account? Register"),
      ),
    );
  }



  Widget get _loginButton {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.redAccent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        onPressed: () async {
          await AppSharedPref.register(
            "KongUsa",
            "<EMAIL>",
            "123456",
          );

          if (_formKey.currentState!.validate()) {
            String email = _emailController.text.trim();
            String password = _passwordController.text.trim();

            bool isValid = await AppSharedPref.login(email, password);
            if (isValid) {
              AppRoute.key.currentState!.pushReplacementNamed(
                AppRoute.mainScreen,
              );
            } else {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text("Invalid email or password")),
                );
              }
            }
          }
        },
        child: const Text(
          "Login",
          style: TextStyle(fontSize: 16, color: Colors.white),
        ),
      ),
    );
  }
}
