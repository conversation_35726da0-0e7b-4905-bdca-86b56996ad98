import 'package:flutter/material.dart';

enum NotificationType {
  promo,
  menuAlert,
  loyaltyPoints,
  order,
  general,
}

class NotificationItem {
  final String id;
  final NotificationType type;
  final String title;
  final String description;
  final DateTime timestamp;
  final bool isRead;
  final IconData icon;
  final Color iconColor;

  NotificationItem({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.timestamp,
    this.isRead = false,
    required this.icon,
    required this.iconColor,
  });

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  NotificationItem copyWith({
    String? id,
    NotificationType? type,
    String? title,
    String? description,
    DateTime? timestamp,
    bool? isRead,
    IconData? icon,
    Color? iconColor,
  }) {
    return NotificationItem(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      icon: icon ?? this.icon,
      iconColor: iconColor ?? this.iconColor,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.index,
      'title': title,
      'description': description,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'isRead': isRead,
    };
  }

  factory NotificationItem.fromMap(Map<String, dynamic> map) {
    final type = NotificationType.values[map['type'] ?? 0];
    return NotificationItem(
      id: map['id'] ?? '',
      type: type,
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp'] ?? 0),
      isRead: map['isRead'] ?? false,
      icon: _getIconForType(type),
      iconColor: _getColorForType(type),
    );
  }

  static IconData _getIconForType(NotificationType type) {
    switch (type) {
      case NotificationType.promo:
        return Icons.local_offer;
      case NotificationType.menuAlert:
        return Icons.restaurant_menu;
      case NotificationType.loyaltyPoints:
        return Icons.card_giftcard;
      case NotificationType.order:
        return Icons.shopping_bag;
      case NotificationType.general:
        return Icons.notifications;
    }
  }

  static Color _getColorForType(NotificationType type) {
    switch (type) {
      case NotificationType.promo:
        return Colors.orange;
      case NotificationType.menuAlert:
        return Colors.red;
      case NotificationType.loyaltyPoints:
        return Colors.amber;
      case NotificationType.order:
        return Colors.green;
      case NotificationType.general:
        return Colors.blue;
    }
  }
}

// Sample notifications data
final List<NotificationItem> sampleNotifications = [
  // Today
  NotificationItem(
    id: '1',
    type: NotificationType.promo,
    title: 'Daily Promo',
    description: 'Enjoy 20% off all latte flavors - only until 5 PM today! Treat yourself to a cup of happiness now!',
    timestamp: DateTime.now().subtract(const Duration(hours: 2)),
    icon: Icons.local_offer,
    iconColor: Colors.orange,
  ),
  NotificationItem(
    id: '2',
    type: NotificationType.menuAlert,
    title: 'New Menu Alert',
    description: 'Introducing: Caramel Sea Salt Cold Brew a perfect blend of sweet, salty, and smooth. Be the first to try it today!',
    timestamp: DateTime.now().subtract(const Duration(hours: 4)),
    icon: Icons.restaurant_menu,
    iconColor: Colors.red,
  ),
  NotificationItem(
    id: '3',
    type: NotificationType.loyaltyPoints,
    title: 'Loyalty Points Reminder',
    description: 'You\'re just 4 points away from a FREE Coffee! Check your points and redeem before they expire!',
    timestamp: DateTime.now().subtract(const Duration(hours: 6)),
    icon: Icons.card_giftcard,
    iconColor: Colors.amber,
  ),
  // Yesterday
  NotificationItem(
    id: '4',
    type: NotificationType.promo,
    title: 'Daily Promo',
    description: 'Enjoy 20% off all latte flavors - only until 5 PM today! Treat yourself to a cup of happiness now!',
    timestamp: DateTime.now().subtract(const Duration(days: 1, hours: 2)),
    icon: Icons.local_offer,
    iconColor: Colors.orange,
  ),
  NotificationItem(
    id: '5',
    type: NotificationType.menuAlert,
    title: 'New Menu Alert',
    description: 'Introducing: Caramel Sea Salt Cold Brew a perfect blend of sweet, salty, and smooth. Be the first to try it today!',
    timestamp: DateTime.now().subtract(const Duration(days: 1, hours: 4)),
    icon: Icons.restaurant_menu,
    iconColor: Colors.red,
  ),
  NotificationItem(
    id: '6',
    type: NotificationType.loyaltyPoints,
    title: 'Loyalty Points Reminder',
    description: 'You\'re just 4 points away from a FREE Coffee! Check your points and redeem before they expire!',
    timestamp: DateTime.now().subtract(const Duration(days: 1, hours: 6)),
    icon: Icons.card_giftcard,
    iconColor: Colors.amber,
  ),
];
