import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';

class TranslationService {
  static const String _languageKey = 'selected_language';
  static const Locale _fallbackLocale = Locale('en', 'US');
  
  // Supported locales
  static const List<Locale> supportedLocales = [
    Locale('en', 'US'), // English
    Locale('kh', 'KM'), // Khmer
  ];

  // Language names for display
  static const Map<String, String> languageNames = {
    'en_US': 'English',
    'kh_KM': 'ខ្មែរ (Khmer)',
  };

  // Language flags/icons
  static const Map<String, String> languageFlags = {
    'en_US': '🇺🇸',
    'kh_KM': '🇰🇭',
  };

  /// Get the current locale
  static Locale get currentLocale => Get.locale ?? _fallbackLocale;

  /// Get the current language code (e.g., 'en_US', 'kh_KM')
  static String get currentLanguageCode => 
      '${currentLocale.languageCode}_${currentLocale.countryCode}';

  /// Get the current language name for display
  static String get currentLanguageName => 
      languageNames[currentLanguageCode] ?? 'English';

  /// Get the current language flag
  static String get currentLanguageFlag => 
      languageFlags[currentLanguageCode] ?? '🇺🇸';

  /// Check if current language is Khmer
  static bool get isKhmer => currentLocale.languageCode == 'kh';

  /// Check if current language is English
  static bool get isEnglish => currentLocale.languageCode == 'en';

  /// Initialize the translation service
  static Future<void> init() async {
    final prefs = await SharedPreferences.getInstance();
    final savedLanguage = prefs.getString(_languageKey);
    
    if (savedLanguage != null) {
      final parts = savedLanguage.split('_');
      if (parts.length == 2) {
        final locale = Locale(parts[0], parts[1]);
        await changeLanguage(locale);
      }
    }
  }

  /// Change the app language
  static Future<void> changeLanguage(Locale locale) async {
    if (supportedLocales.contains(locale)) {
      // Save preference first
      await _saveLanguagePreference(locale);

      // Update locale
      Get.updateLocale(locale);

      // Force rebuild of all widgets
      Get.forceAppUpdate();

      // Show success message
      Get.snackbar(
        'Language Changed',
        'Language has been changed successfully',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    }
  }

  /// Change language by language code
  static Future<void> changeLanguageByCode(String languageCode) async {
    final parts = languageCode.split('_');
    if (parts.length == 2) {
      final locale = Locale(parts[0], parts[1]);
      await changeLanguage(locale);
    }
  }

  /// Toggle between English and Khmer
  static Future<void> toggleLanguage() async {
    if (isEnglish) {
      await changeLanguage(const Locale('kh', 'KM'));
    } else {
      await changeLanguage(const Locale('en', 'US'));
    }
  }

  /// Save language preference
  static Future<void> _saveLanguagePreference(Locale locale) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_languageKey, '${locale.languageCode}_${locale.countryCode}');
  }

  /// Get translation with fallback
  static String tr(String key, {Map<String, String>? args}) {
    try {
      String translation = key.tr;
      
      // Replace arguments if provided
      if (args != null) {
        args.forEach((placeholder, value) {
          translation = translation.replaceAll('@$placeholder', value);
        });
      }
      
      return translation;
    } catch (e) {
      // Return the key itself if translation fails
      return key;
    }
  }

  /// Get translation with plural support
  static String trPlural(String key, int count, {Map<String, String>? args}) {
    String pluralKey = count == 1 ? key : '${key}_plural';
    return tr(pluralKey, args: args);
  }

  /// Format currency based on locale
  static String formatCurrency(double amount) {
    if (isKhmer) {
      return '${amount.toStringAsFixed(2)} រៀល';
    } else {
      return '\$${amount.toStringAsFixed(2)}';
    }
  }

  /// Format date based on locale
  static String formatDate(DateTime date) {
    if (isKhmer) {
      // Khmer date format
      return '${date.day}/${date.month}/${date.year}';
    } else {
      // English date format
      return '${date.month}/${date.day}/${date.year}';
    }
  }

  /// Get localized month names
  static List<String> get monthNames {
    if (isKhmer) {
      return [
        'មករា', 'កុម្ភៈ', 'មីនា', 'មេសា', 'ឧសភា', 'មិថុនា',
        'កក្កដា', 'សីហា', 'កញ្ញា', 'តុលា', 'វិច្ឆិកា', 'ធ្នូ'
      ];
    } else {
      return [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
    }
  }

  /// Get localized day names
  static List<String> get dayNames {
    if (isKhmer) {
      return ['អាទិត្យ', 'ច័ន្ទ', 'អង្គារ', 'ពុធ', 'ព្រហស្បតិ៍', 'សុក្រ', 'សៅរ៍'];
    } else {
      return ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    }
  }

  /// Get text direction based on locale
  static TextDirection get textDirection {
    // Khmer is left-to-right like English
    return TextDirection.ltr;
  }

  /// Get font family based on locale
  static String? get fontFamily {
    if (isKhmer) {
      // You can specify a Khmer font here if you have one
      return 'KhmerFont'; // Replace with your actual Khmer font name
    }
    return null; // Use default font for English
  }

  /// Show language selection dialog
  static void showLanguageDialog(BuildContext context) {
    Get.bottomSheet(
      StatefulBuilder(
        builder: (BuildContext context, StateSetter setState) {
          return Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Text(
                    tr('language'),
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),

                // Language options
                ...supportedLocales.map((locale) {
                  final languageCode = '${locale.languageCode}_${locale.countryCode}';
                  final isSelected = currentLanguageCode == languageCode;

                  return ListTile(
                    leading: Text(
                      languageFlags[languageCode] ?? '',
                      style: const TextStyle(fontSize: 24),
                    ),
                    title: Text(languageNames[languageCode] ?? ''),
                    trailing: isSelected
                        ? const Icon(Icons.check, color: Colors.green)
                        : null,
                    onTap: () async {
                      await changeLanguage(locale);
                      setState(() {}); // Update the dialog state
                      // Close dialog after a short delay to show the checkmark
                      Future.delayed(const Duration(milliseconds: 300), () {
                        if (Get.isBottomSheetOpen == true) {
                          Get.back();
                        }
                      });
                    },
                  );
                }),
              ],
            ),
          );
        },
      ),
      backgroundColor: Colors.transparent,
    );
  }
}
