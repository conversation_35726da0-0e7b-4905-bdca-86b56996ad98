import 'package:flutter/material.dart';
import 'package:test_pro/screen/outlet_finder_screen.dart';
import 'package:test_pro/screen/enhanced_outlet_finder_screen.dart';
import 'package:test_pro/screen/outlet_favorites_screen.dart';
import 'package:test_pro/services/outlet_favorites_service.dart';

class ComprehensiveOutletDemo extends StatefulWidget {
  const ComprehensiveOutletDemo({super.key});

  @override
  State<ComprehensiveOutletDemo> createState() => _ComprehensiveOutletDemoState();
}

class _ComprehensiveOutletDemoState extends State<ComprehensiveOutletDemo> {
  final OutletFavoritesService _favoritesService = OutletFavoritesService();
  int _favoritesCount = 0;

  @override
  void initState() {
    super.initState();
    _loadFavoritesCount();
  }

  Future<void> _loadFavoritesCount() async {
    final count = await _favoritesService.getFavoritesCount();
    setState(() {
      _favoritesCount = count;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Coffee Vortex - Outlet Finder'),
        backgroundColor: Colors.brown,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.brown, Colors.brown.shade700],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Find Your Perfect',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.w300,
                    ),
                  ),
                  const Text(
                    'Coffee Experience',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Discover outlets near you with our interactive map',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.9),
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Stats Row
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Outlets',
                    '3',
                    Icons.store,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Favorites',
                    _favoritesCount.toString(),
                    Icons.favorite,
                    Colors.red,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Open Now',
                    '2',
                    Icons.access_time,
                    Colors.green,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Main Features
            const Text(
              'Explore Features',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Basic Outlet Finder
            _buildFeatureCard(
              title: 'Basic Outlet Finder',
              description: 'Simple map view with outlet locations and basic information',
              icon: Icons.map,
              color: Colors.blue,
              features: [
                'Interactive map with outlet markers',
                'Search outlets by name or address',
                'View outlet details and operating hours',
                'Check service availability',
              ],
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const OutletFinderScreen(),
                  ),
                );
              },
            ),
            
            const SizedBox(height: 16),
            
            // Enhanced Outlet Finder
            _buildFeatureCard(
              title: 'Enhanced Outlet Finder',
              description: 'Advanced features with filtering, favorites, and detailed information',
              icon: Icons.filter_list,
              color: Colors.purple,
              features: [
                'Advanced filtering by services and rating',
                'Favorite outlets management',
                'Enhanced markers with ratings',
                'Real-time results count',
              ],
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const EnhancedOutletFinderScreen(),
                  ),
                );
              },
            ),
            
            const SizedBox(height: 16),
            
            // Favorites Screen
            _buildFeatureCard(
              title: 'Favorite Outlets',
              description: 'Manage and view your favorite coffee outlets',
              icon: Icons.favorite,
              color: Colors.red,
              features: [
                'View all favorite outlets',
                'Quick access to outlet details',
                'Direct calling and directions',
                'Remove from favorites',
              ],
              onTap: () async {
                final favorites = await _favoritesService.getFavorites();
                if (context.mounted) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => OutletFavoritesScreen(
                        favoriteOutletIds: favorites,
                      ),
                    ),
                  ).then((_) => _loadFavoritesCount());
                }
              },
            ),
            
            const SizedBox(height: 24),
            
            // Quick Actions
            const Text(
              'Quick Actions',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () async {
                      final messenger = ScaffoldMessenger.of(context);
                      await _favoritesService.clearFavorites();
                      _loadFavoritesCount();
                      if (mounted) {
                        messenger.showSnackBar(
                          const SnackBar(
                            content: Text('All favorites cleared'),
                            backgroundColor: Colors.orange,
                          ),
                        );
                      }
                    },
                    icon: const Icon(Icons.clear_all),
                    label: const Text('Clear Favorites'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      _showInfoDialog();
                    },
                    icon: const Icon(Icons.info_outline),
                    label: const Text('About'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.brown,
                      side: const BorderSide(color: Colors.brown),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureCard({
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required List<String> features,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(icon, color: color, size: 24),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          description,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Colors.grey[400],
                  ),
                ],
              ),
              const SizedBox(height: 12),
              ...features.map((feature) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      size: 16,
                      color: color,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        feature,
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  ],
                ),
              )),
            ],
          ),
        ),
      ),
    );
  }

  void _showInfoDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('About Outlet Finder'),
        content: const Text(
          'This demo showcases different implementations of an outlet finder for Coffee Vortex. '
          'Features include interactive maps, search functionality, filtering, favorites management, '
          'and detailed outlet information.\n\n'
          'Built with Flutter and designed to match modern mobile app standards.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
