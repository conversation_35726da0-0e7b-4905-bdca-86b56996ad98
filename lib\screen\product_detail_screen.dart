import 'package:flutter/material.dart';
import 'package:test_pro/Widgets/product_detail/all_select.dart';
import 'package:test_pro/Widgets/product_detail/btn_add_to_card.dart';
import 'package:test_pro/Widgets/product_detail/description.dart';
import 'package:test_pro/Widgets/product_detail/product_image.dart';
import 'package:test_pro/models/home/<USER>';
import 'package:test_pro/services/favorite_service.dart';
import 'package:test_pro/services/auth_service.dart';

class ProductDetailScreen extends StatefulWidget {
  final Product product;

  const ProductDetailScreen({super.key, required this.product});

  @override
  State<ProductDetailScreen> createState() => _ProductDetailScreenState();
}

class _ProductDetailScreenState extends State<ProductDetailScreen> {
  String selectedTemperature = "Cold";
  String selectedSize = "Medium";
  String selectedSweetness = "Normal";
  String selectedTopping = "Normal";
  int quantity = 1;
  bool isFavorite = false;

  @override
  void initState() {
    super.initState();
    _loadFavoriteState();
  }

  Future<void> _loadFavoriteState() async {
    final productId = widget.product.id ?? 0;
    final favorite = await FavoriteService.isFavorite(productId);
    setState(() {
      isFavorite = favorite;
    });
  }

  Future<void> _toggleFavorite() async {
    // Check authentication first
    final hasAuth = await AuthService.requireAuth(
      context,
      message: 'Please log in to add items to your favorites.',
    );

    if (!hasAuth) return;

    await FavoriteService.toggleFavorite(widget.product);
    setState(() {
      isFavorite = !isFavorite;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(isFavorite ? 'Added to favorites' : 'Removed from favorites'),
          backgroundColor: isFavorite ? Colors.green : Colors.red,
        ),
      );
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: Icon(
              isFavorite ? Icons.favorite : Icons.favorite_border,
              color: isFavorite ? Colors.red : Colors.black,
            ),
            onPressed: _toggleFavorite,
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product Title
                  Center(
                    child: Text(
                      widget.product.name,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Center(
                    child: Text(
                      "With Hot Berry",
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  ProductImage(product: widget.product),
                  const SizedBox(height: 24),
                  Description(product: widget.product),
                  const SizedBox(height: 24),
                  AllSelect(),
                  const SizedBox(height: 24),

                  // Quantity Selector
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.green[700],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: IconButton(
                          icon: const Icon(Icons.remove, color: Colors.white),
                          onPressed: () {
                            if (quantity > 1) {
                              setState(() {
                                quantity--;
                              });
                            }
                          },
                        ),
                      ),
                      const SizedBox(width: 20),
                      Text(
                        quantity.toString(),
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 20),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.green[700],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: IconButton(
                          icon: const Icon(Icons.add, color: Colors.white),
                          onPressed: () {
                            setState(() {
                              quantity++;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                ],
              ),
            ),
          ),
          // Add to Cart Button
          AddToCartButton(
            product: widget.product,
            quantity: quantity,
            selectedTemperature: selectedTemperature,
            selectedSize: selectedSize,
            selectedSweetness: selectedSweetness,
            selectedTopping: selectedTopping,
          ),
        ],
      ),
    );
  }
}
