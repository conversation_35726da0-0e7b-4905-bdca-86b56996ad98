class FavoriteItem {
  final int productId;
  final String name;
  final String category;
  final double price;
  final String? image;
  final String? description;

  FavoriteItem({
    required this.productId,
    required this.name,
    required this.category,
    required this.price,
    this.image,
    this.description,
  });

  Map<String, dynamic> toMap() {
    return {
      'productId': productId,
      'name': name,
      'category': category,
      'price': price,
      'image': image,
      'description': description,
    };
  }

  factory FavoriteItem.fromMap(Map<String, dynamic> map) {
    return FavoriteItem(
      productId: map['productId'],
      name: map['name'],
      category: map['category'],
      price: map['price'] is int ? (map['price'] as int).toDouble() : map['price'],
      image: map['image'],
      description: map['description'],
    );
  }

  @override
  String toString() {
    return 'FavoriteItem(productId: $productId, name: $name, category: $category, price: $price)';
  }
}
