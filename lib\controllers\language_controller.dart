import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageController extends GetxController {
  static LanguageController get instance => Get.find();
  
  // Observable current locale
  final Rx<Locale> _currentLocale = const Locale('en', 'US').obs;
  
  // Getters
  Locale get currentLocale => _currentLocale.value;
  String get currentLanguageCode => '${currentLocale.languageCode}_${currentLocale.countryCode}';
  
  // Supported locales
  static const List<Locale> supportedLocales = [
    Locale('en', 'US'), // English
    Locale('kh', 'KM'), // Khmer
  ];

  // Language names for display
  static const Map<String, String> languageNames = {
    'en_US': 'English',
    'kh_KM': 'ខ្មែរ (Khmer)',
  };

  // Language flags/icons
  static const Map<String, String> languageFlags = {
    'en_US': '🇺🇸',
    'kh_KM': '🇰🇭',
  };

  @override
  void onInit() {
    super.onInit();
    _loadSavedLanguage();
  }

  /// Load saved language preference
  Future<void> _loadSavedLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLanguage = prefs.getString('selected_language');
      
      if (savedLanguage != null) {
        final parts = savedLanguage.split('_');
        if (parts.length == 2) {
          final locale = Locale(parts[0], parts[1]);
          if (supportedLocales.contains(locale)) {
            _currentLocale.value = locale;
            Get.updateLocale(locale);
          }
        }
      }
    } catch (e) {
      print('Error loading saved language: $e');
    }
  }

  /// Change the app language
  Future<void> changeLanguage(Locale locale) async {
    if (supportedLocales.contains(locale)) {
      try {
        // Update the observable
        _currentLocale.value = locale;
        
        // Update GetX locale
        Get.updateLocale(locale);
        
        // Save preference
        await _saveLanguagePreference(locale);
        
        // Show success message
        Get.snackbar(
          'Language Changed',
          'Language has been changed successfully',
          snackPosition: SnackPosition.TOP,
          duration: const Duration(seconds: 2),
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        
        // Force app update
        Get.forceAppUpdate();
        
      } catch (e) {
        print('Error changing language: $e');
        Get.snackbar(
          'Error',
          'Failed to change language',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  /// Save language preference
  Future<void> _saveLanguagePreference(Locale locale) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('selected_language', '${locale.languageCode}_${locale.countryCode}');
    } catch (e) {
      print('Error saving language preference: $e');
    }
  }

  /// Get current language name
  String get currentLanguageName => languageNames[currentLanguageCode] ?? 'English';

  /// Get current language flag
  String get currentLanguageFlag => languageFlags[currentLanguageCode] ?? '🇺🇸';

  /// Check if current language is Khmer
  bool get isKhmer => currentLocale.languageCode == 'kh';

  /// Check if current language is English
  bool get isEnglish => currentLocale.languageCode == 'en';

  /// Toggle between English and Khmer
  Future<void> toggleLanguage() async {
    if (isEnglish) {
      await changeLanguage(const Locale('kh', 'KM'));
    } else {
      await changeLanguage(const Locale('en', 'US'));
    }
  }

  /// Show language selection dialog
  void showLanguageDialog(BuildContext context) {
    Get.bottomSheet(
      Obx(() => Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Text(
                'language'.tr,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // Language options
            ...supportedLocales.map((locale) {
              final languageCode = '${locale.languageCode}_${locale.countryCode}';
              final isSelected = currentLanguageCode == languageCode;
              
              return ListTile(
                leading: Text(
                  languageFlags[languageCode] ?? '',
                  style: const TextStyle(fontSize: 24),
                ),
                title: Text(languageNames[languageCode] ?? ''),
                trailing: isSelected 
                    ? const Icon(Icons.check, color: Colors.green)
                    : null,
                onTap: () async {
                  await changeLanguage(locale);
                  // Close dialog after a short delay to show the checkmark
                  Future.delayed(const Duration(milliseconds: 500), () {
                    if (Get.isBottomSheetOpen == true) {
                      Get.back();
                    }
                  });
                },
              );
            }),
          ],
        ),
      )),
      backgroundColor: Colors.transparent,
    );
  }
}
