# Coffee Vortex - Outlet Finder System

A comprehensive Flutter implementation of outlet finder functionality that replicates and enhances the map-based design shown in your mockup. This system provides multiple screens and services for finding, managing, and interacting with coffee shop locations.

## 🎯 Complete Implementation

This implementation includes **3 different outlet finder screens** with increasing levels of functionality, plus supporting services and models.

## ✨ Screen Features Comparison

### 1. 🗺️ Basic Outlet Finder (`OutletFinderScreen`)
**Perfect replica of your mockup design**
- ✅ Interactive map with outlet markers
- ✅ Search bar for outlets
- ✅ Bottom sheet outlet preview
- ✅ Full-screen outlet details
- ✅ Operating hours display
- ✅ Service availability (Pickup, Delivery, Dine-in)
- ✅ Favorite button integration
- ✅ Color-coded markers (green/grey/orange)

### 2. 🔧 Enhanced Outlet Finder (`EnhancedOutletFinderScreen`)
**Advanced version with filtering and enhanced UX**
- ✅ All basic features PLUS:
- 🆕 Advanced filtering panel (services, rating, operational status)
- 🆕 Filter chips for quick service selection
- 🆕 Rating slider filter
- 🆕 Results count display
- 🆕 Enhanced markers with rating badges
- 🆕 Favorite indicators on markers
- 🆕 Back button and improved navigation
- 🆕 Service chips in outlet preview

### 3. ❤️ Favorites Screen (`OutletFavoritesScreen`)
**Dedicated favorites management**
- ✅ Grid/list view of favorite outlets
- ✅ Quick remove from favorites
- ✅ Direct calling and directions
- ✅ Empty state with call-to-action
- ✅ Service availability display
- ✅ Outlet status indicators

### 🎨 Common Design Features
- Matches your provided mockup design
- Smooth transitions and animations
- Professional UI with proper spacing
- Consistent color scheme (brown theme)
- Material Design 3 components
- Responsive layout for different screen sizes

## 📁 Files Created

### 🎯 Core Screens
1. **`lib/screen/outlet_finder_screen.dart`** - Basic outlet finder (matches your mockup)
2. **`lib/screen/enhanced_outlet_finder_screen.dart`** - Advanced version with filtering & favorites
3. **`lib/screen/outlet_favorites_screen.dart`** - Dedicated favorites management screen

### 🏗️ Models & Services
- **`lib/models/outlet_model.dart`** - Outlet data model with sample data and business logic
- **`lib/services/outlet_favorites_service.dart`** - Persistent favorites management with SharedPreferences

### 🎮 Demo Screens
- **`lib/demo/outlet_finder_demo.dart`** - Simple demo for basic outlet finder
- **`lib/demo/comprehensive_outlet_demo.dart`** - Complete demo showcasing all features

### 🎨 Assets
- **`assets/images/outlet/`** - Directory for outlet images (outlet1.jpg, outlet2.jpg, outlet3.jpg, default.jpg)
- **`assets/images/map_placeholder.png`** - Map background placeholder

### 🔧 Integration
- **Updated `lib/Widgets/home/<USER>
- **Updated `lib/screen/main_screen.dart`** - Fixed navigation structure
- **Updated `pubspec.yaml`** - Added outlet assets

## 🚀 Quick Start

### 1. Run the Comprehensive Demo
```dart
import 'package:test_pro/demo/comprehensive_outlet_demo.dart';

// Navigate to the demo screen to see all features
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const ComprehensiveOutletDemo(),
  ),
);
```

### 2. Basic Navigation
```dart
// Basic outlet finder
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const OutletFinderScreen(),
  ),
);

// Enhanced outlet finder with filters
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const EnhancedOutletFinderScreen(),
  ),
);

// Favorites screen
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const OutletFavoritesScreen(),
  ),
);
```

### 3. Integration with Home Screen
The outlet finder is already integrated with the existing `LocateBranchHome` widget. Tapping on the branch carousel will navigate to the basic outlet finder screen.

### 4. Using Favorites Service
```dart
final favoritesService = OutletFavoritesService();

// Add to favorites
await favoritesService.addFavorite('outlet_id');

// Remove from favorites
await favoritesService.removeFavorite('outlet_id');

// Toggle favorite status
await favoritesService.toggleFavorite('outlet_id');

// Check if favorite
bool isFavorite = await favoritesService.isFavorite('outlet_id');

// Get all favorites
Set<String> favorites = await favoritesService.getFavorites();
```

### Customizing Outlets
Modify the `sampleOutlets` list in `lib/models/outlet_model.dart` to add your own outlet data:

```dart
final List<Outlet> sampleOutlets = [
  Outlet(
    id: '1',
    name: 'Your Outlet Name',
    address: 'Your Address',
    latitude: 34.0837,
    longitude: -118.3365,
    image: 'assets/images/outlet/your_image.jpg',
    isOperational: true,
    operatingHours: {
      'Monday': '08:00 AM - 10:00 PM',
      // ... other days
    },
    services: ['pickup', 'delivery', 'dine-in'],
    phone: '+****************',
    rating: 4.5,
    reviewCount: 127,
  ),
  // Add more outlets...
];
```

## Screen States

### 1. Map View
- Shows all outlets as markers on a custom map
- Search bar at the top
- Tappable markers to select outlets

### 2. Outlet Preview
- Bottom sheet with outlet image and basic info
- Shows outlet name, address, and operational status
- "INFO" button to view full details

### 3. Outlet Details
- Full-screen view with complete outlet information
- Large outlet image
- Operating hours for all days
- Service options (Pickup/Delivery)
- "VIEW MAPS" button for navigation

## Outlet Model Properties

```dart
class Outlet {
  final String id;              // Unique identifier
  final String name;            // Outlet name
  final String address;         // Full address
  final double latitude;        // GPS coordinates
  final double longitude;       // GPS coordinates
  final String? image;          // Image asset path
  final bool isOperational;     // Operating status
  final Map<String, String> operatingHours; // Daily hours
  final List<String> services;  // Available services
  final String? phone;          // Contact number
  final double? rating;         // User rating
  final int? reviewCount;       // Number of reviews
}
```

## Customization Options

### Colors
- Operational outlets: Green markers
- Closed outlets: Grey markers
- Selected outlet: Orange marker

### Services
- `pickup` - Pickup service available
- `delivery` - Delivery service available
- `dine-in` - Dine-in service available

### Map Styling
The map background uses a custom painter (`MapPainter`) that draws:
- Blue curved lines (representing rivers/waterways)
- Green circles (representing parks/green areas)

## Future Enhancements

### Potential Features to Add
1. **Real Map Integration**: Replace custom painter with Google Maps or Apple Maps
2. **GPS Location**: Show user's current location
3. **Directions**: Integrate with navigation apps
4. **Real-time Status**: Live outlet status updates
5. **Favorites**: Save favorite outlets
6. **Reviews**: User reviews and ratings
7. **Filters**: Filter by services, rating, distance
8. **Notifications**: Outlet status change notifications

### API Integration
The current implementation uses static data. To integrate with a real backend:

1. Replace `sampleOutlets` with API calls
2. Add loading states
3. Implement error handling
4. Add refresh functionality

## Testing

Run the demo to test the functionality:
```dart
// Use the demo screen
import 'package:test_pro/demo/outlet_finder_demo.dart';

// Or navigate directly
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const OutletFinderScreen(),
  ),
);
```

## Dependencies

The outlet finder uses standard Flutter widgets and doesn't require additional packages beyond what's already in your project.

## Assets Setup

Make sure your `pubspec.yaml` includes:
```yaml
flutter:
  assets:
    - assets/images/
    - assets/images/coffee/
    - assets/images/outlet/
```

The screen will work with placeholder images if specific outlet images are not available.
