class UserProfile {
  final String name;
  final String email;
  final String phone;
  final String? profileImage;

  UserProfile({
    required this.name,
    required this.email,
    required this.phone,
    this.profileImage,
  });

  // Convert to Map for SharedPreferences storage
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'email': email,
      'phone': phone,
      'profileImage': profileImage,
    };
  }

  // Create from Map
  factory UserProfile.fromMap(Map<String, dynamic> map) {
    return UserProfile(
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      phone: map['phone'] ?? '',
      profileImage: map['profileImage'],
    );
  }

  // Create a copy with updated fields
  UserProfile copyWith({
    String? name,
    String? email,
    String? phone,
    String? profileImage,
  }) {
    return UserProfile(
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      profileImage: profileImage ?? this.profileImage,
    );
  }

  @override
  String toString() {
    return 'UserProfile(name: $name, email: $email, phone: $phone, profileImage: $profileImage)';
  }
}
