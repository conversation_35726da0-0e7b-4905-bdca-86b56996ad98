import 'package:flutter/material.dart';
import 'package:test_pro/screen/Card/cart_screen.dart';
import 'package:test_pro/screen/favorites_screen.dart';
import 'package:test_pro/screen/home_screen.dart';
import 'package:test_pro/screen/profile_screen.dart';
import 'package:test_pro/services/auth_service.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;
  List<Widget> screenList = [
    HomeScreen(),
    CartScreen(),
    CartScreen(),
    FavoritesScreen(),
    ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: _body, bottomNavigationBar: bottomNav);
  }

  Widget get _body {
    return screenList[_currentIndex];
  }

  Widget get bottomNav {
    final items = [
      BottomNavigationBarItem(icon: Icon(Icons.home), label: "Home"),
      BottomNavigationBarItem(icon: Icon(Icons.shopping_cart), label: "Cart"),
      BottomNavigationBarItem(icon: Icon(Icons.all_inbox_outlined), label: "All"),
      BottomNavigationBarItem(icon: Icon(Icons.favorite), label: "Favorite"),
      BottomNavigationBarItem(icon: Icon(Icons.person), label: "Profile"),
    ];
    return BottomNavigationBar(
      items: items,
      selectedItemColor: Colors.red,
      unselectedItemColor: Colors.blueGrey,
      showSelectedLabels: true,
      showUnselectedLabels: true,
      currentIndex: _currentIndex,
      onTap: (index) async {
        // Check authentication for favorites tab (index 1)
        if (index == 1) {
          final hasAuth = await AuthService.requireAuth(
            context,
            message: 'Please log in to view your favorites.',
          );

          if (!hasAuth) return;
        }

        setState(() {
          _currentIndex = index;
        });
      },
    );
  }
}
