import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:test_pro/services/theme_service.dart';

class SettingView extends StatelessWidget {
  const SettingView({super.key});
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Settings Header
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          child: Text(
            'setting'.tr,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),

        // Language Setting
        Card(
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            onTap: () {
              Get.bottomSheet(
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: const BoxDecoration(
                    color: Color.fromARGB(255, 226, 223, 223),
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(16)),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextButton(
                        onPressed: () {
                          var locale = const Locale('en', 'US');
                          Get.updateLocale(locale);
                          Get.back();
                        },
                        child: const Text('English'),
                      ),
                      const Divider(color: Colors.grey),
                      TextButton(
                        onPressed: () {
                          var locale = const Locale('kh', 'KM');
                          Get.updateLocale(locale);
                          Get.back();
                        },
                        child: const Text('Khmer'),
                      ),
                    ],
                  ),
                ),
                backgroundColor: Colors.transparent,
              );
            },
            title: Text('Language'), // Uses GetX translation for language
            leading: const Icon(Icons.language),
            trailing: const Icon(Icons.arrow_forward_ios),
          ),
        ),

        // Theme Setting
        Card(
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            onTap: () {
              _showThemeDialog(context);
            },
            title: Text('Theme'), // Uses GetX translation for theme
            leading: AnimatedBuilder(
              animation: ThemeService(),
              builder: (context, child) {
                return Icon(ThemeService().themeIcon);
              },
            ),
            subtitle: AnimatedBuilder(
              animation: ThemeService(),
              builder: (context, child) {
                return Text(ThemeService().themeModeString);
              },
            ),
            trailing: const Icon(Icons.arrow_forward_ios),
          ),
        ),
      ],
    );
  }

  void _showThemeDialog(BuildContext context) {
    Get.bottomSheet(
      Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Text(
                'Choose Theme',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // Light Theme Option
            ListTile(
              leading: const Icon(Icons.light_mode),
              title: const Text('Light'),
              trailing: ThemeService().isLightMode
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () async {
                await ThemeService().setLightTheme();
                Get.back();
              },
            ),

            // Dark Theme Option
            ListTile(
              leading: const Icon(Icons.dark_mode),
              title: const Text('Dark'),
              trailing: ThemeService().isDarkMode
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () async {
                await ThemeService().setDarkTheme();
                Get.back();
              },
            ),

            // System Theme Option
            ListTile(
              leading: const Icon(Icons.brightness_auto),
              title: const Text('System'),
              trailing: ThemeService().isSystemMode
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () async {
                await ThemeService().setSystemTheme();
                Get.back();
              },
            ),
          ],
        ),
      ),
      backgroundColor: Colors.transparent,
    );
  }
}
