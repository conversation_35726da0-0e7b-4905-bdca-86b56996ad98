class Product {
  final int? id;
  final String name;
  final String category;
  final double price;
  final int quantity;
  final String? image;
  final String? description;

  Product({
    this.id,
    required this.name,
    required this.category,
    required this.price,
    required this.quantity,
    this.image,
    this.description,
  });

  Product.simple(this.name, this.category)
    : id = null,
      price = 0.0,
      quantity = 1,
      image = null,
      description = null;

  // Convert Product to Map for database storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'price': price,
      'quantity': quantity,
      'image': image,
      'description': description,
    };
  }

  // Create Product from Map (database result)
  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id'],
      name: map['name'] ?? '',
      category: map['category'] ?? '',
      price: map['price'] is int ? (map['price'] as int).toDouble() : map['price'] ?? 0.0,
      quantity: map['quantity'] ?? 0,
      image: map['image'],
      description: map['description'],
    );
  }

  @override
  String toString() {
    return 'Product(id: $id, name: $name, category: $category, price: $price, quantity: $quantity, image: $image, description: $description)';
  }
}

final List<Product> allProducts = [
    Product(
      id: 1,
      name: "Espresso",
      category: "Coffee",
      price: 3.50,
      quantity: 1,
      image: "assets/images/coffee/espresso.png",
      description: "A bold Americano blended with a refreshing hint of sweet berries and a smooth finish. This drink will leave you feeling energized and ready to take on the day. Perfect for those who love their coffee with a twist."
    ),
    Product(
      id: 2,
      name: "Latte",
      category: "Coffee",
      price: 4.25,
      quantity: 1,
      image: "assets/images/coffee/ice_latte.png",
      description: "Creamy steamed milk combined with rich espresso, creating the perfect balance of smooth and bold flavors."
    ),
  ];