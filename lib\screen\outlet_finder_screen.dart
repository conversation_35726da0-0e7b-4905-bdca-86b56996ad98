import 'package:flutter/material.dart';
import 'package:test_pro/Widgets/Outlet/build_map_maker.dart';
import 'package:test_pro/Widgets/Outlet/map_painter.dart';
import 'package:test_pro/Widgets/Outlet/outlet_detail.dart';
import 'package:test_pro/Widgets/Outlet/outlet_preview.dart';
import 'package:test_pro/models/outlet_model.dart';
import 'package:test_pro/services/outlet_favorites_service.dart';
import 'package:test_pro/services/auth_service.dart';

class OutletFinderScreen extends StatefulWidget {
  const OutletFinderScreen({super.key});

  @override
  State<OutletFinderScreen> createState() => _OutletFinderScreenState();
}

class _OutletFinderScreenState extends State<OutletFinderScreen> with OutletFavoritesMixin {
  final TextEditingController _searchController = TextEditingController();
  bool _showOutletDetails = false;
  final List<Outlet> _outlets = sampleOutlets;
  List<Outlet> _filteredOutlets = sampleOutlets;
  Outlet? _selectedOutlet;

  @override
  void initState() {
    super.initState();
    _selectedOutlet = _outlets.first;
    _searchController.addListener(_onSearchChanged);
  }

  void _onSearchChanged() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredOutlets = _outlets.where((outlet) {
        return outlet.name.toLowerCase().contains(query) ||
               outlet.address.toLowerCase().contains(query);
      }).toList();
    });
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _selectOutlet(Outlet outlet) {
    setState(() {
      _selectedOutlet = outlet;
      _showOutletDetails = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // Map Background (placeholder)
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/images/map_placeholder.png'),
                fit: BoxFit.cover,
              ),
            ),
            child: Container(
              color: Colors.grey.withValues(alpha: 0.1),
              child: CustomPaint(
                painter: MapPainter(),
              ),
            ),
          ),
          
          // Search Bar
          Positioned(
            top: MediaQuery.of(context).padding.top + 10,
            left: 16,
            right: 16,
            child: Container(
              height: 50,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Outlet 1',
                  hintStyle: TextStyle(color: Colors.grey[600]),
                  prefixIcon: const Icon(Icons.search, color: Colors.grey),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                ),
              ),
            ),
          ),

          // Map Markers (placeholder positions)
          ..._filteredOutlets.asMap().entries.map((entry) {
            final index = entry.key;
            final outlet = entry.value;
            final isSelected = outlet.id == _selectedOutlet?.id;

            // Position outlets at different locations on the map
            double top = 200 + (index * 50.0);
            double left = 50 + (index * 100.0);

            // Keep within screen bounds
            if (left > MediaQuery.of(context).size.width - 100) {
              left = MediaQuery.of(context).size.width - 100;
              top += 50;
            }

            return Positioned(
              top: top,
              left: left,
              child: BuildMapMaker(
                outlet: outlet,
                isSelected: isSelected,
                isFavorite: isOutletFavorite(outlet.id),
                onTap: () => _selectOutlet(outlet),
              ),
            );
          }),

          // Bottom Sheet - Outlet Info
          if (!_showOutletDetails && _selectedOutlet != null)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: OutletPreview(
                selectedOutlet: _selectedOutlet,
                isFavorite: isOutletFavorite(_selectedOutlet?.id ?? ''),
                onInfoTap: () {
                  setState(() {
                    _showOutletDetails = true;
                  });
                },
                onFavoriteTap: () => toggleOutletFavorite(_selectedOutlet?.id ?? ''),
              ),
            ),

          // Full Outlet Details
          if (_showOutletDetails && _selectedOutlet != null)
            Positioned(
              top: MediaQuery.of(context).padding.top + 80,
              left: 0,
              right: 0,
              bottom: 0,
              child: OutletDetail(
                selectedOutlet: _selectedOutlet,
                onClose: () {
                  setState(() {
                    _showOutletDetails = false;
                  });
                },
              ),
            ),
        ],
      ),
    );
  }

}
