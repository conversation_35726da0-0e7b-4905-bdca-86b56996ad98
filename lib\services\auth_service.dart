import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthService {
  // Singleton pattern
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  /// Check if user is logged in (not a guest)
  Future<bool> isLoggedIn() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final username = prefs.getString('username');
      final email = prefs.getString('email');
      
      // User is logged in if they have both username and email, and username is not "Guest"
      return username != null && 
             email != null && 
             username.isNotEmpty && 
             email.isNotEmpty &&
             username.toLowerCase() != 'guest';
    } catch (e) {
      return false;
    }
  }

  /// Check if user is a guest
  Future<bool> isGuest() async {
    return !(await isLoggedIn());
  }

  /// Get current user's username
  Future<String> getCurrentUsername() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('username') ?? 'Guest';
    } catch (e) {
      return 'Guest';
    }
  }

  /// Get current user's email
  Future<String> getCurrentUserEmail() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('email') ?? '';
    } catch (e) {
      return '';
    }
  }

  /// Check if user has valid credentials
  Future<bool> hasValidCredentials() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final email = prefs.getString('email');
      final password = prefs.getString('password');
      
      return email != null && 
             password != null && 
             email.isNotEmpty && 
             password.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Get user login status info
  Future<Map<String, dynamic>> getUserStatus() async {
    final isLoggedIn = await this.isLoggedIn();
    final username = await getCurrentUsername();
    final email = await getCurrentUserEmail();

    return {
      'isLoggedIn': isLoggedIn,
      'isGuest': !isLoggedIn,
      'username': username,
      'email': email,
    };
  }

  /// Show login required alert with custom styling
  static void showLoginAlert(BuildContext context, {String? message}) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: Theme.of(context).cardColor,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Alert Icon
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.warning_rounded,
                    color: Colors.orange,
                    size: 30,
                  ),
                ),

                const SizedBox(height: 16),

                // Title
                Text(
                  'Oops! You need to log in first to continue!',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ),

                const SizedBox(height: 12),

                // Message
                if (message != null)
                  Text(
                    message,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),

                const SizedBox(height: 24),

                // Buttons
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Cancel',
                          style: TextStyle(
                            color: Theme.of(context).textTheme.bodyMedium?.color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          // Navigate to login screen
                          Navigator.pushNamed(context, 'loginScreen');
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          'Log In',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Check authentication and show alert if needed
  static Future<bool> requireAuth(BuildContext context, {String? message}) async {
    final isLoggedIn = await AuthService().isLoggedIn();

    if (!isLoggedIn && context.mounted) {
      showLoginAlert(context, message: message);
      return false;
    }

    return isLoggedIn;
  }
}
