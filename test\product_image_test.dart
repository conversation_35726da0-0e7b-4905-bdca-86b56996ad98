import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:test_pro/Widgets/product_detail/product_image.dart';
import 'package:test_pro/models/home/<USER>';

void main() {
  group('ProductImage Widget Tests', () {
    testWidgets('should display product image when image path is provided', (WidgetTester tester) async {
      // Create a test product with image
      final product = Product(
        id: 1,
        name: 'Test Coffee',
        category: 'Coffee',
        price: 4.50,
        quantity: 1,
        image: 'assets/images/coffee/espresso.png',
        description: 'Test coffee description',
      );

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProductImage(product: product),
          ),
        ),
      );

      // Verify the widget is rendered
      expect(find.byType(ProductImage), findsOneWidget);
      expect(find.byType(Container), findsWidgets);
      expect(find.byType(ClipRRect), findsOneWidget);
    });

    testWidgets('should display coffee icon when no image is provided', (WidgetTester tester) async {
      // Create a test product without image
      final product = Product(
        id: 2,
        name: 'Test Coffee No Image',
        category: 'Coffee',
        price: 3.50,
        quantity: 1,
        image: null,
        description: 'Test coffee without image',
      );

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProductImage(product: product),
          ),
        ),
      );

      // Verify the coffee icon is displayed
      expect(find.byType(ProductImage), findsOneWidget);
      expect(find.byIcon(Icons.coffee), findsOneWidget);
    });

    testWidgets('should have correct widget structure', (WidgetTester tester) async {
      final product = Product(
        id: 3,
        name: 'Styled Coffee',
        category: 'Coffee',
        price: 5.00,
        quantity: 1,
        image: 'assets/images/coffee/espresso.png',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProductImage(product: product),
          ),
        ),
      );

      // Verify the widget structure
      expect(find.byType(Center), findsOneWidget);
      expect(find.byType(Container), findsWidgets);
      expect(find.byType(ClipRRect), findsOneWidget);
      expect(find.byType(ProductImage), findsOneWidget);
    });

    testWidgets('should handle error when image fails to load', (WidgetTester tester) async {
      final product = Product(
        id: 4,
        name: 'Invalid Image Coffee',
        category: 'Coffee',
        price: 4.00,
        quantity: 1,
        image: 'assets/images/coffee/nonexistent.png',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProductImage(product: product),
          ),
        ),
      );

      // The widget should still render without throwing errors
      expect(find.byType(ProductImage), findsOneWidget);
      expect(find.byType(ClipRRect), findsOneWidget);
    });
  });
}
