import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:test_pro/models/notification_model.dart';

class NotificationService {
  static Future<String> _getFilePath() async {
    final dir = await getApplicationDocumentsDirectory();
    return '${dir.path}/notifications.json';
  }

  static Future<List<NotificationItem>> getNotifications() async {
    final path = await _getFilePath();
    final file = File(path);

    if (!(await file.exists())) {
      // Return sample notifications if no file exists
      await _saveNotifications(sampleNotifications);
      return sampleNotifications;
    }

    try {
      final lines = await file.readAsLines();
      return lines
          .where((line) => line.trim().isNotEmpty)
          .map((line) {
            try {
              return NotificationItem.fromMap(jsonDecode(line));
            } catch (e) {
              return null;
            }
          })
          .whereType<NotificationItem>()
          .toList();
    } catch (e) {
      // Return sample notifications if file is corrupted
      return sampleNotifications;
    }
  }

  static Future<void> _saveNotifications(List<NotificationItem> notifications) async {
    final path = await _getFilePath();
    final file = File(path);
    final sink = file.openWrite();
    for (final notification in notifications) {
      sink.writeln(jsonEncode(notification.toMap()));
    }
    await sink.close();
  }

  static Future<void> addNotification(NotificationItem notification) async {
    final notifications = await getNotifications();
    notifications.insert(0, notification); // Add to the beginning
    await _saveNotifications(notifications);
  }

  static Future<void> markAsRead(String notificationId) async {
    final notifications = await getNotifications();
    final index = notifications.indexWhere((n) => n.id == notificationId);
    
    if (index != -1) {
      notifications[index] = notifications[index].copyWith(isRead: true);
      await _saveNotifications(notifications);
    }
  }

  static Future<void> markAllAsRead() async {
    final notifications = await getNotifications();
    final updatedNotifications = notifications
        .map((n) => n.copyWith(isRead: true))
        .toList();
    await _saveNotifications(updatedNotifications);
  }

  static Future<void> deleteNotification(String notificationId) async {
    final notifications = await getNotifications();
    notifications.removeWhere((n) => n.id == notificationId);
    await _saveNotifications(notifications);
  }

  static Future<void> clearAllNotifications() async {
    final path = await _getFilePath();
    final file = File(path);
    if (await file.exists()) {
      await file.delete();
    }
  }

  static Future<int> getUnreadCount() async {
    final notifications = await getNotifications();
    return notifications.where((n) => !n.isRead).length;
  }

  static Map<String, List<NotificationItem>> groupNotificationsByDate(
      List<NotificationItem> notifications) {
    final Map<String, List<NotificationItem>> grouped = {};
    final now = DateTime.now();

    for (final notification in notifications) {
      final difference = now.difference(notification.timestamp).inDays;
      String key;

      if (difference == 0) {
        key = 'Today';
      } else if (difference == 1) {
        key = 'Yesterday';
      } else if (difference < 7) {
        key = '$difference days ago';
      } else {
        key = 'Earlier';
      }

      if (!grouped.containsKey(key)) {
        grouped[key] = [];
      }
      grouped[key]!.add(notification);
    }

    return grouped;
  }

  // Helper method to create sample notifications for testing
  static Future<void> createSampleNotifications() async {
    await _saveNotifications(sampleNotifications);
  }

  // Helper method to add a promo notification
  static Future<void> addPromoNotification(String title, String description) async {
    final notification = NotificationItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: NotificationType.promo,
      title: title,
      description: description,
      timestamp: DateTime.now(),
      icon: Icons.local_offer,
      iconColor: Colors.orange,
    );
    await addNotification(notification);
  }

  // Helper method to add a menu alert notification
  static Future<void> addMenuAlertNotification(String title, String description) async {
    final notification = NotificationItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: NotificationType.menuAlert,
      title: title,
      description: description,
      timestamp: DateTime.now(),
      icon: Icons.restaurant_menu,
      iconColor: Colors.red,
    );
    await addNotification(notification);
  }

  // Helper method to add a loyalty points notification
  static Future<void> addLoyaltyPointsNotification(String title, String description) async {
    final notification = NotificationItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: NotificationType.loyaltyPoints,
      title: title,
      description: description,
      timestamp: DateTime.now(),
      icon: Icons.card_giftcard,
      iconColor: Colors.amber,
    );
    await addNotification(notification);
  }
}
