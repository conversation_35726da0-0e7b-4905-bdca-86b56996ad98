import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:test_pro/models/user_model.dart';
import 'package:test_pro/services/user_profile_service.dart';
import 'package:test_pro/services/auth_service.dart';

class UserController extends GetxController {
  // Observable user profile
  final Rx<UserProfile?> _userProfile = Rx<UserProfile?>(null);
  final RxBool _isLoading = false.obs;
  final RxBool _isLoggedIn = false.obs;

  // Getters
  UserProfile? get userProfile => _userProfile.value;
  bool get isLoading => _isLoading.value;
  bool get isLoggedIn => _isLoggedIn.value;
  String get userName => _userProfile.value?.name ?? 'Guest';
  String get userEmail => _userProfile.value?.email ?? '';
  String get userPhone => _userProfile.value?.phone ?? '';
  String? get userProfileImage => _userProfile.value?.profileImage;

  // Firebase Auth instance
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final UserProfileService _profileService = UserProfileService();

  @override
  void onInit() {
    super.onInit();
    _initializeUser();
    
    // Listen to Firebase Auth state changes
    _auth.authStateChanges().listen((User? user) {
      _handleAuthStateChange(user);
    });
  }

  /// Initialize user data on app start
  Future<void> _initializeUser() async {
    _isLoading.value = true;
    
    try {
      // Check if user is logged in
      final loggedIn = await AuthService().isLoggedIn();
      _isLoggedIn.value = loggedIn;
      
      if (loggedIn) {
        // Load user profile from storage
        await _loadUserProfile();
      } else {
        // Set guest user
        _setGuestUser();
      }
    } catch (e) {
      print('Error initializing user: $e');
      _setGuestUser();
    } finally {
      _isLoading.value = false;
    }
  }

  /// Handle Firebase Auth state changes
  void _handleAuthStateChange(User? user) async {
    if (user != null) {
      // User signed in
      _isLoggedIn.value = true;
      await _syncWithFirebaseUser(user);
    } else {
      // User signed out
      _isLoggedIn.value = false;
      _setGuestUser();
    }
  }

  /// Sync user data with Firebase user
  Future<void> _syncWithFirebaseUser(User firebaseUser) async {
    try {
      // Get existing profile or create new one
      UserProfile? existingProfile = await _profileService.getUserProfile();
      
      // Create profile with Firebase data if none exists
      if (existingProfile == null || existingProfile.name == 'Guest') {
        final newProfile = UserProfile(
          name: firebaseUser.displayName ?? firebaseUser.email?.split('@')[0] ?? 'User',
          email: firebaseUser.email ?? '',
          phone: firebaseUser.phoneNumber ?? '',
          profileImage: firebaseUser.photoURL,
        );
        
        await _profileService.saveUserProfile(newProfile);
        _userProfile.value = newProfile;
      } else {
        // Update existing profile with Firebase data if needed
        final updatedProfile = existingProfile.copyWith(
          email: firebaseUser.email ?? existingProfile.email,
        );
        
        if (updatedProfile.email != existingProfile.email) {
          await _profileService.saveUserProfile(updatedProfile);
        }
        
        _userProfile.value = updatedProfile;
      }
    } catch (e) {
      print('Error syncing with Firebase user: $e');
      await _loadUserProfile();
    }
  }

  /// Load user profile from storage
  Future<void> _loadUserProfile() async {
    try {
      final profile = await _profileService.getUserProfile();
      _userProfile.value = profile;
    } catch (e) {
      print('Error loading user profile: $e');
      _setGuestUser();
    }
  }

  /// Set guest user
  void _setGuestUser() {
    _userProfile.value = UserProfile(
      name: 'Guest',
      email: '',
      phone: '',
    );
  }

  /// Update user profile
  Future<bool> updateProfile(UserProfile newProfile) async {
    try {
      _isLoading.value = true;
      
      final success = await _profileService.saveUserProfile(newProfile);
      
      if (success) {
        _userProfile.value = newProfile;
        
        // Show success message
        Get.snackbar(
          'Success',
          'Profile updated successfully',
          snackPosition: SnackPosition.TOP,
          duration: const Duration(seconds: 2),
        );
        
        return true;
      } else {
        Get.snackbar(
          'Error',
          'Failed to update profile',
          snackPosition: SnackPosition.TOP,
        );
        return false;
      }
    } catch (e) {
      print('Error updating profile: $e');
      Get.snackbar(
        'Error',
        'An error occurred while updating profile',
        snackPosition: SnackPosition.TOP,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update specific profile field
  Future<bool> updateProfileField(String field, String value) async {
    if (_userProfile.value == null) return false;
    
    try {
      UserProfile updatedProfile;
      
      switch (field) {
        case 'name':
          updatedProfile = _userProfile.value!.copyWith(name: value);
          break;
        case 'email':
          updatedProfile = _userProfile.value!.copyWith(email: value);
          break;
        case 'phone':
          updatedProfile = _userProfile.value!.copyWith(phone: value);
          break;
        case 'profileImage':
          updatedProfile = _userProfile.value!.copyWith(profileImage: value);
          break;
        default:
          return false;
      }
      
      return await updateProfile(updatedProfile);
    } catch (e) {
      print('Error updating profile field: $e');
      return false;
    }
  }

  /// Refresh user data
  Future<void> refreshUser() async {
    await _initializeUser();
  }

  /// Clear user data on logout
  void clearUser() {
    _userProfile.value = null;
    _isLoggedIn.value = false;
    _setGuestUser();
  }
}
