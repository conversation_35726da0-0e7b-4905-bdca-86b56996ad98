import 'dart:async';
import 'package:flutter/material.dart';
import 'package:test_pro/Widgets/search/no_result.dart';
import 'package:test_pro/Widgets/search/search_result.dart';
import 'package:test_pro/models/home/<USER>';
import 'package:test_pro/services/search_service.dart';
import 'package:test_pro/services/auth_service.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Product> _filteredProducts = [];
  List<String> _recentSearches = [];
  bool _isLoading = true;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _checkAuthAndLoadData();
  }

  Future<void> _checkAuthAndLoadData() async {
    // Check if user is logged in
    final isLoggedIn = await AuthService().isLoggedIn();

    if (!isLoggedIn && mounted) {
      // Show login alert and go back
      WidgetsBinding.instance.addPostFrameCallback((_) {
        AuthService.showLoginAlert(
          context,
          message: 'Please log in to search for products.',
        );
        Navigator.of(context).pop();
      });
      return;
    }

    // Load data for logged-in users
    _loadProducts();
    _loadRecentSearches();
    _loadAllProducts(); // Load all products initially
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadProducts() async {
    setState(() {
      _isLoading = true;
    });

    // Products are loaded directly from SearchService when needed
    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _loadAllProducts() async {
    // Load all products initially to show them when search screen opens
    final allProducts = SearchService.getAllProducts();
    setState(() {
      _filteredProducts = allProducts;
    });
  }

  Future<void> _loadRecentSearches() async {
    final recentSearches = await SearchService.getRecentSearches();
    setState(() {
      _recentSearches = recentSearches;
    });
  }

  void _performSearch(String query) {
    // Cancel previous timer
    _debounceTimer?.cancel();

    // Set up new timer for debouncing
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      if (query.isEmpty) {
        // Show all products when search is empty
        _loadAllProducts();
        return;
      }

      final filtered = SearchService.searchProducts(query);

      setState(() {
        _filteredProducts = filtered;
      });

      // Add to recent searches only if query is not empty
      if (query.trim().isNotEmpty) {
        SearchService.addToRecentSearches(query);
        _loadRecentSearches(); // Refresh recent searches
      }
    });
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _filteredProducts = [];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Theme.of(context).iconTheme.color),
          onPressed: () => Navigator.pop(context),
        ),
        title: SizedBox(
          height: 40,
          child: TextField(
            controller: _searchController,
            autofocus: true,
            decoration: InputDecoration(
              hintText: 'Search for coffee, tea, or category...',
              hintStyle: TextStyle(color: Theme.of(context).hintColor),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(20),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Theme.of(context).inputDecorationTheme.fillColor,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              prefixIcon: Icon(Icons.search, color: Theme.of(context).iconTheme.color, size: 20),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: Icon(Icons.clear, color: Theme.of(context).iconTheme.color, size: 20),
                      onPressed: _clearSearch,
                    )
                  : null,
            ),
            onChanged: _performSearch,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_filteredProducts.isEmpty && _searchController.text.isNotEmpty) {
      return const NoResult();
    } else {
      return SearchResult(
        filteredProducts: _filteredProducts,
        recentSearches: _recentSearches,
        searchController: _searchController,
        onSearchTap: (search) {
          _searchController.text = search;
          _performSearch(search);
        },
      );
    }
  }

}
