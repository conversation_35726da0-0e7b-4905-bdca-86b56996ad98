import 'package:flutter/material.dart';
import 'package:test_pro/models/outlet_model.dart';
import 'package:test_pro/services/auth_service.dart';
import 'package:test_pro/screen/outlet_finder_screen.dart';

class OutletFavoritesScreen extends StatefulWidget {
  final Set<String> favoriteOutletIds;
  
  const OutletFavoritesScreen({
    super.key,
    this.favoriteOutletIds = const {},
  });

  @override
  State<OutletFavoritesScreen> createState() => _OutletFavoritesScreenState();
}

class _OutletFavoritesScreenState extends State<OutletFavoritesScreen> {
  late Set<String> _favoriteOutletIds;
  List<Outlet> _favoriteOutlets = [];

  @override
  void initState() {
    super.initState();
    _favoriteOutletIds = Set.from(widget.favoriteOutletIds);
    _checkAuthAndLoadFavorites();
  }

  Future<void> _checkAuthAndLoadFavorites() async {
    // Check if user is logged in
    final isLoggedIn = await AuthService().isLoggedIn();

    if (!isLoggedIn && mounted) {
      // Show login alert and go back
      WidgetsBinding.instance.addPostFrameCallback((_) {
        AuthService.showLoginAlert(
          context,
          message: 'Please log in to view your favorite outlets.',
        );
        Navigator.of(context).pop();
      });
      return;
    }

    // Load favorites for logged-in users
    _loadFavoriteOutlets();
  }

  void _loadFavoriteOutlets() {
    setState(() {
      _favoriteOutlets = sampleOutlets
          .where((outlet) => _favoriteOutletIds.contains(outlet.id))
          .toList();
    });
  }

  void _removeFavorite(String outletId) {
    setState(() {
      _favoriteOutletIds.remove(outletId);
      _loadFavoriteOutlets();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Favorite Outlets'),
        backgroundColor: Colors.brown,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const OutletFinderScreen(),
                ),
              );
            },
            icon: const Icon(Icons.map),
            tooltip: 'Find Outlets',
          ),
        ],
      ),
      body: _favoriteOutlets.isEmpty
          ? _buildEmptyState()
          : _buildFavoritesList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_border,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Favorite Outlets',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add outlets to your favorites to see them here',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const OutletFinderScreen(),
                ),
              );
            },
            icon: const Icon(Icons.search),
            label: const Text('Find Outlets'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.brown,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFavoritesList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _favoriteOutlets.length,
      itemBuilder: (context, index) {
        final outlet = _favoriteOutlets[index];
        return _buildOutletCard(outlet);
      },
    );
  }

  Widget _buildOutletCard(Outlet outlet) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: outlet.isOperational ? Colors.green : Colors.grey,
          child: const Icon(Icons.local_cafe, color: Colors.white),
        ),
        title: Text(
          outlet.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(outlet.address),
        trailing: IconButton(
          icon: const Icon(Icons.favorite, color: Colors.red),
          onPressed: () => _removeFavorite(outlet.id),
        ),
        onTap: () {
          // Navigate to outlet details
        },
      ),
    );
  }


}
